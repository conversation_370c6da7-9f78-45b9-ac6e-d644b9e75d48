import jwt from 'jsonwebtoken';
import { IUser } from '@/models/User';
import { logger } from '@/utils/logger';

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  isEmailVerified: boolean;
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export class JWTService {
  private static readonly ACCESS_TOKEN_SECRET = process.env.JWT_SECRET!;
  private static readonly REFRESH_TOKEN_SECRET = process.env.JWT_REFRESH_SECRET!;
  private static readonly ACCESS_TOKEN_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '15m';
  private static readonly REFRESH_TOKEN_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

  /**
   * Generate access token
   */
  static generateAccessToken(user: IUser): string {
    const payload: JWTPayload = {
      userId: user._id.toString(),
      email: user.email,
      role: user.role,
      isEmailVerified: user.isEmailVerified
    };

    return jwt.sign(payload, this.ACCESS_TOKEN_SECRET, {
      expiresIn: this.ACCESS_TOKEN_EXPIRES_IN,
      issuer: 'theramea',
      audience: 'theramea-users'
    });
  }

  /**
   * Generate refresh token
   */
  static generateRefreshToken(user: IUser): string {
    const payload = {
      userId: user._id.toString(),
      email: user.email,
      tokenType: 'refresh'
    };

    return jwt.sign(payload, this.REFRESH_TOKEN_SECRET, {
      expiresIn: this.REFRESH_TOKEN_EXPIRES_IN,
      issuer: 'theramea',
      audience: 'theramea-users'
    });
  }

  /**
   * Generate both access and refresh tokens
   */
  static generateTokenPair(user: IUser): TokenPair {
    return {
      accessToken: this.generateAccessToken(user),
      refreshToken: this.generateRefreshToken(user)
    };
  }

  /**
   * Verify access token
   */
  static verifyAccessToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.ACCESS_TOKEN_SECRET, {
        issuer: 'theramea',
        audience: 'theramea-users'
      }) as JWTPayload;
    } catch (error) {
      logger.error('Access token verification failed:', error);
      throw new Error('Invalid or expired access token');
    }
  }

  /**
   * Verify refresh token
   */
  static verifyRefreshToken(token: string): any {
    try {
      return jwt.verify(token, this.REFRESH_TOKEN_SECRET, {
        issuer: 'theramea',
        audience: 'theramea-users'
      });
    } catch (error) {
      logger.error('Refresh token verification failed:', error);
      throw new Error('Invalid or expired refresh token');
    }
  }

  /**
   * Extract token from Authorization header
   */
  static extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) return null;
    
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }
    
    return parts[1];
  }

  /**
   * Generate email verification token
   */
  static generateEmailVerificationToken(email: string): string {
    const payload = {
      email,
      purpose: 'email-verification'
    };

    return jwt.sign(payload, this.ACCESS_TOKEN_SECRET, {
      expiresIn: '24h',
      issuer: 'theramea',
      audience: 'theramea-users'
    });
  }

  /**
   * Verify email verification token
   */
  static verifyEmailVerificationToken(token: string): { email: string } {
    try {
      const payload = jwt.verify(token, this.ACCESS_TOKEN_SECRET, {
        issuer: 'theramea',
        audience: 'theramea-users'
      }) as any;

      if (payload.purpose !== 'email-verification') {
        throw new Error('Invalid token purpose');
      }

      return { email: payload.email };
    } catch (error) {
      logger.error('Email verification token verification failed:', error);
      throw new Error('Invalid or expired email verification token');
    }
  }

  /**
   * Generate password reset token
   */
  static generatePasswordResetToken(email: string): string {
    const payload = {
      email,
      purpose: 'password-reset'
    };

    return jwt.sign(payload, this.ACCESS_TOKEN_SECRET, {
      expiresIn: '1h',
      issuer: 'theramea',
      audience: 'theramea-users'
    });
  }

  /**
   * Verify password reset token
   */
  static verifyPasswordResetToken(token: string): { email: string } {
    try {
      const payload = jwt.verify(token, this.ACCESS_TOKEN_SECRET, {
        issuer: 'theramea',
        audience: 'theramea-users'
      }) as any;

      if (payload.purpose !== 'password-reset') {
        throw new Error('Invalid token purpose');
      }

      return { email: payload.email };
    } catch (error) {
      logger.error('Password reset token verification failed:', error);
      throw new Error('Invalid or expired password reset token');
    }
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      logger.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) return true;
    return expiration < new Date();
  }
}
