function e(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function t(e,t,n,r,i,o,a){try{var s=e[o](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function n(e){return function(){var n=this,r=arguments;return new Promise((function(i,o){var a=e.apply(n,r);function s(e){t(a,i,o,s,c,"next",e)}function c(e){t(a,i,o,s,c,"throw",e)}s(void 0)}))}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e){var t=function(e,t){if("object"!==i(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===i(t)?t:String(t)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,o(r.key),r)}}function s(e,t,n){return t&&a(e.prototype,t),n&&a(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function c(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function u(e,t){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},u(e,t)}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u(e,t)}function d(e,t){if(t&&("object"===i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return c(e)}function h(e){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},h(e)}function p(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var g,m="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},y={},_={get exports(){return y},set exports(e){y=e}},b="object"==typeof Reflect?Reflect:null,w=b&&"function"==typeof b.apply?b.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};g=b&&"function"==typeof b.ownKeys?b.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var S=Number.isNaN||function(e){return e!=e};function k(){k.init.call(this)}_.exports=k,y.once=function(e,t){return new Promise((function(n,r){function i(n){e.removeListener(t,o),r(n)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",i),n([].slice.call(arguments))}D(e,t,o,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&D(e,"error",t,n)}(e,i,{once:!0})}))},k.EventEmitter=k,k.prototype._events=void 0,k.prototype._eventsCount=0,k.prototype._maxListeners=void 0;var E=10;function M(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function C(e){return void 0===e._maxListeners?k.defaultMaxListeners:e._maxListeners}function T(e,t,n,r){var i,o,a,s;if(M(n),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),a=o[t]),void 0===a)a=o[t]=n,++e._eventsCount;else if("function"==typeof a?a=o[t]=r?[n,a]:[a,n]:r?a.unshift(n):a.push(n),(i=C(e))>0&&a.length>i&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=a.length,s=c,console&&console.warn&&console.warn(s)}return e}function O(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function j(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=O.bind(r);return i.listener=n,r.wrapFn=i,i}function P(e,t,n){var r=e._events;if(void 0===r)return[];var i=r[t];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(i):A(i,i.length)}function x(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function A(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function D(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){r.once&&e.removeEventListener(t,i),n(o)}))}}Object.defineProperty(k,"defaultMaxListeners",{enumerable:!0,get:function(){return E},set:function(e){if("number"!=typeof e||e<0||S(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");E=e}}),k.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},k.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||S(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},k.prototype.getMaxListeners=function(){return C(this)},k.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var a=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var s=i[e];if(void 0===s)return!1;if("function"==typeof s)w(s,this,t);else{var c=s.length,u=A(s,c);for(n=0;n<c;++n)w(u[n],this,t)}return!0},k.prototype.addListener=function(e,t){return T(this,e,t,!1)},k.prototype.on=k.prototype.addListener,k.prototype.prependListener=function(e,t){return T(this,e,t,!0)},k.prototype.once=function(e,t){return M(t),this.on(e,j(this,e,t)),this},k.prototype.prependOnceListener=function(e,t){return M(t),this.prependListener(e,j(this,e,t)),this},k.prototype.removeListener=function(e,t){var n,r,i,o,a;if(M(t),void 0===(r=this._events))return this;if(void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){a=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,i),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,a||t)}return this},k.prototype.off=k.prototype.removeListener,k.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},k.prototype.listeners=function(e){return P(this,e,!0)},k.prototype.rawListeners=function(e){return P(this,e,!1)},k.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):x.call(e,t)},k.prototype.listenerCount=x,k.prototype.eventNames=function(){return this._eventsCount>0?g(this._events):[]};var L="function"==typeof Map,N="function"==typeof Set,I="function"==typeof WeakSet,R=Object.keys,B=function(e,t){return e&&"object"==typeof e&&t.add(e)},F=function(e,t,n,r){for(var i,o=0;o<e.length;o++)if(n((i=e[o])[0],t[0],r)&&n(i[1],t[1],r))return!0;return!1},U=function(e,t,n,r){for(var i=0;i<e.length;i++)if(n(e[i],t,r))return!0;return!1},V=function(e,t){return e===t||e!=e&&t!=t},$=function(e){return e.constructor===Object},G=function(e){return"function"==typeof e.then},q=function(e){return!(!e.$$typeof||!e._store)},Y=function(e){return function(t){var n=e||t;return function(e,t,r){void 0===r&&(r=I?new WeakSet:Object.create({_values:[],add:function(e){this._values.push(e)},has:function(e){return!!~this._values.indexOf(e)}}));var i=r.has(e),o=r.has(t);return i||o?i&&o:(B(e,r),B(t,r),n(e,t,r))}}},z=function(e){var t=[];return e.forEach((function(e,n){return t.push([n,e])})),t},J=function(e){var t=[];return e.forEach((function(e){return t.push(e)})),t},W=function(e,t,n,r){var i,o=R(e),a=R(t);if(o.length!==a.length)return!1;for(var s=0;s<o.length;s++){if(i=o[s],!U(a,i,V))return!1;if(("_owner"!==i||!q(e)||!q(t))&&!n(e[i],t[i],r))return!1}return!0},H=Array.isArray,K=function(e){var t="function"==typeof e?e(n):n;function n(e,n,r){if(V(e,n))return!0;var i=typeof e;if(i!==typeof n||"object"!==i||!e||!n)return!1;if($(e)&&$(n))return W(e,n,t,r);var o=H(e),a=H(n);if(o||a)return o===a&&function(e,t,n,r){if(e.length!==t.length)return!1;for(var i=0;i<e.length;i++)if(!n(e[i],t[i],r))return!1;return!0}(e,n,t,r);var s=e instanceof Date,c=n instanceof Date;if(s||c)return s===c&&V(e.getTime(),n.getTime());var u,l,d=e instanceof RegExp,h=n instanceof RegExp;if(d||h)return d===h&&(l=n,(u=e).source===l.source&&u.global===l.global&&u.ignoreCase===l.ignoreCase&&u.multiline===l.multiline&&u.unicode===l.unicode&&u.sticky===l.sticky&&u.lastIndex===l.lastIndex);if(G(e)||G(n))return e===n;if(L){var p=e instanceof Map,f=n instanceof Map;if(p||f)return p===f&&function(e,t,n,r){if(e.size!==t.size)return!1;for(var i=z(e),o=z(t),a=0;a<i.length;a++)if(!F(o,i[a],n,r)||!F(i,o[a],n,r))return!1;return!0}(e,n,t,r)}if(N){var v=e instanceof Set,g=n instanceof Set;if(v||g)return v===g&&function(e,t,n,r){if(e.size!==t.size)return!1;for(var i=J(e),o=J(t),a=0;a<i.length;a++)if(!U(o,i[a],n,r)||!U(i,o[a],n,r))return!1;return!0}(e,n,t,r)}return W(e,n,t,r)}return n};K(Y()),K(Y(V));var Q=K();K((function(){return V}));const X={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},Z={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},ee={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},te={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},ne={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class re{static getFirstMatch(e,t){const n=t.match(e);return n&&n.length>0&&n[1]||""}static getSecondMatch(e,t){const n=t.match(e);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(e,t,n){if(e.test(t))return n}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){const t=e.split(".").splice(0,2).map((e=>parseInt(e,10)||0));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){const t=e.split(".").splice(0,2).map((e=>parseInt(e,10)||0));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,t,n=!1){const r=re.getVersionPrecision(e),i=re.getVersionPrecision(t);let o=Math.max(r,i),a=0;const s=re.map([e,t],(e=>{const t=o-re.getVersionPrecision(e),n=e+new Array(t+1).join(".0");return re.map(n.split("."),(e=>new Array(20-e.length).join("0")+e)).reverse()}));for(n&&(a=o-Math.min(r,i)),o-=1;o>=a;){if(s[0][o]>s[1][o])return 1;if(s[0][o]===s[1][o]){if(o===a)return 0;o-=1}else if(s[0][o]<s[1][o])return-1}}static map(e,t){const n=[];let r;if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n}static find(e,t){let n,r;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(n=0,r=e.length;n<r;n+=1){const r=e[n];if(t(r,n))return r}}static assign(e,...t){const n=e;let r,i;if(Object.assign)return Object.assign(e,...t);for(r=0,i=t.length;r<i;r+=1){const e=t[r];if("object"==typeof e&&null!==e){Object.keys(e).forEach((t=>{n[t]=e[t]}))}}return e}static getBrowserAlias(e){return X[e]}static getBrowserTypeByAlias(e){return Z[e]||""}}const ie=/version\/(\d+(\.?_?\d+)+)/i,oe=[{test:[/googlebot/i],describe(e){const t={name:"Googlebot"},n=re.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/opera/i],describe(e){const t={name:"Opera"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opr\/|opios/i],describe(e){const t={name:"Opera"},n=re.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/SamsungBrowser/i],describe(e){const t={name:"Samsung Internet for Android"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Whale/i],describe(e){const t={name:"NAVER Whale Browser"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MZBrowser/i],describe(e){const t={name:"MZ Browser"},n=re.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/focus/i],describe(e){const t={name:"Focus"},n=re.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/swing/i],describe(e){const t={name:"Swing"},n=re.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/coast/i],describe(e){const t={name:"Opera Coast"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){const t={name:"Opera Touch"},n=re.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/yabrowser/i],describe(e){const t={name:"Yandex Browser"},n=re.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/ucbrowser/i],describe(e){const t={name:"UC Browser"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/Maxthon|mxios/i],describe(e){const t={name:"Maxthon"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/epiphany/i],describe(e){const t={name:"Epiphany"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/puffin/i],describe(e){const t={name:"Puffin"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sleipnir/i],describe(e){const t={name:"Sleipnir"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/k-meleon/i],describe(e){const t={name:"K-Meleon"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/micromessenger/i],describe(e){const t={name:"WeChat"},n=re.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/qqbrowser/i],describe(e){const t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},n=re.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/msie|trident/i],describe(e){const t={name:"Internet Explorer"},n=re.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/\sedg\//i],describe(e){const t={name:"Microsoft Edge"},n=re.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/edg([ea]|ios)/i],describe(e){const t={name:"Microsoft Edge"},n=re.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/vivaldi/i],describe(e){const t={name:"Vivaldi"},n=re.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/seamonkey/i],describe(e){const t={name:"SeaMonkey"},n=re.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/sailfish/i],describe(e){const t={name:"Sailfish"},n=re.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return n&&(t.version=n),t}},{test:[/silk/i],describe(e){const t={name:"Amazon Silk"},n=re.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/phantom/i],describe(e){const t={name:"PhantomJS"},n=re.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/slimerjs/i],describe(e){const t={name:"SlimerJS"},n=re.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t={name:"BlackBerry"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(web|hpw)[o0]s/i],describe(e){const t={name:"WebOS Browser"},n=re.getFirstMatch(ie,e)||re.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/bada/i],describe(e){const t={name:"Bada"},n=re.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/tizen/i],describe(e){const t={name:"Tizen"},n=re.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/qupzilla/i],describe(e){const t={name:"QupZilla"},n=re.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){const t={name:"Firefox"},n=re.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/electron/i],describe(e){const t={name:"Electron"},n=re.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/MiuiBrowser/i],describe(e){const t={name:"Miui"},n=re.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/chromium/i],describe(e){const t={name:"Chromium"},n=re.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/chrome|crios|crmo/i],describe(e){const t={name:"Chrome"},n=re.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/GSA/i],describe(e){const t={name:"Google Search"},n=re.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){const t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe(e){const t={name:"Android Browser"},n=re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/playstation 4/i],describe(e){const t={name:"PlayStation 4"},n=re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/safari|applewebkit/i],describe(e){const t={name:"Safari"},n=re.getFirstMatch(ie,e);return n&&(t.version=n),t}},{test:[/.*/i],describe(e){const t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:re.getFirstMatch(t,e),version:re.getSecondMatch(t,e)}}}];var ae=[{test:[/Roku\/DVP/],describe(e){const t=re.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:te.Roku,version:t}}},{test:[/windows phone/i],describe(e){const t=re.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:te.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){const t=re.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),n=re.getWindowsVersionName(t);return{name:te.Windows,version:t,versionName:n}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){const t={name:te.iOS},n=re.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return n&&(t.version=n),t}},{test:[/macintosh/i],describe(e){const t=re.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),n=re.getMacOSVersionName(t),r={name:te.MacOS,version:t};return n&&(r.versionName=n),r}},{test:[/(ipod|iphone|ipad)/i],describe(e){const t=re.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:te.iOS,version:t}}},{test(e){const t=!e.test(/like android/i),n=e.test(/android/i);return t&&n},describe(e){const t=re.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),n=re.getAndroidVersionName(t),r={name:te.Android,version:t};return n&&(r.versionName=n),r}},{test:[/(web|hpw)[o0]s/i],describe(e){const t=re.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),n={name:te.WebOS};return t&&t.length&&(n.version=t),n}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){const t=re.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||re.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||re.getFirstMatch(/\bbb(\d+)/i,e);return{name:te.BlackBerry,version:t}}},{test:[/bada/i],describe(e){const t=re.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:te.Bada,version:t}}},{test:[/tizen/i],describe(e){const t=re.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:te.Tizen,version:t}}},{test:[/linux/i],describe:()=>({name:te.Linux})},{test:[/CrOS/],describe:()=>({name:te.ChromeOS})},{test:[/PlayStation 4/],describe(e){const t=re.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:te.PlayStation4,version:t}}}],se=[{test:[/googlebot/i],describe:()=>({type:"bot",vendor:"Google"})},{test:[/huawei/i],describe(e){const t=re.getFirstMatch(/(can-l01)/i,e)&&"Nova",n={type:ee.mobile,vendor:"Huawei"};return t&&(n.model=t),n}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:()=>({type:ee.tablet,vendor:"Nexus"})},{test:[/ipad/i],describe:()=>({type:ee.tablet,vendor:"Apple",model:"iPad"})},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:()=>({type:ee.tablet,vendor:"Apple",model:"iPad"})},{test:[/kftt build/i],describe:()=>({type:ee.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"})},{test:[/silk/i],describe:()=>({type:ee.tablet,vendor:"Amazon"})},{test:[/tablet(?! pc)/i],describe:()=>({type:ee.tablet})},{test(e){const t=e.test(/ipod|iphone/i),n=e.test(/like (ipod|iphone)/i);return t&&!n},describe(e){const t=re.getFirstMatch(/(ipod|iphone)/i,e);return{type:ee.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:()=>({type:ee.mobile,vendor:"Nexus"})},{test:[/[^-]mobi/i],describe:()=>({type:ee.mobile})},{test:e=>"blackberry"===e.getBrowserName(!0),describe:()=>({type:ee.mobile,vendor:"BlackBerry"})},{test:e=>"bada"===e.getBrowserName(!0),describe:()=>({type:ee.mobile})},{test:e=>"windows phone"===e.getBrowserName(),describe:()=>({type:ee.mobile,vendor:"Microsoft"})},{test(e){const t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:()=>({type:ee.tablet})},{test:e=>"android"===e.getOSName(!0),describe:()=>({type:ee.mobile})},{test:e=>"macos"===e.getOSName(!0),describe:()=>({type:ee.desktop,vendor:"Apple"})},{test:e=>"windows"===e.getOSName(!0),describe:()=>({type:ee.desktop})},{test:e=>"linux"===e.getOSName(!0),describe:()=>({type:ee.desktop})},{test:e=>"playstation 4"===e.getOSName(!0),describe:()=>({type:ee.tv})},{test:e=>"roku"===e.getOSName(!0),describe:()=>({type:ee.tv})}],ce=[{test:e=>"microsoft edge"===e.getBrowserName(!0),describe(e){if(/\sedg\//i.test(e))return{name:ne.Blink};const t=re.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:ne.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){const t={name:ne.Trident},n=re.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:e=>e.test(/presto/i),describe(e){const t={name:ne.Presto},n=re.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test(e){const t=e.test(/gecko/i),n=e.test(/like gecko/i);return t&&!n},describe(e){const t={name:ne.Gecko},n=re.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}},{test:[/(apple)?webkit\/537\.36/i],describe:()=>({name:ne.Blink})},{test:[/(apple)?webkit/i],describe(e){const t={name:ne.WebKit},n=re.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return n&&(t.version=n),t}}];class ue{constructor(e,t=!1){if(null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const e=re.find(oe,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const e=re.find(ae,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){const{name:t}=this.getOS();return e?String(t).toLowerCase()||"":t||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){const{type:t}=this.getPlatform();return e?String(t).toLowerCase()||"":t||""}parsePlatform(){this.parsedResult.platform={};const e=re.find(se,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const e=re.find(ce,(e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some((e=>this.test(e)));throw new Error("Browser's test function is not valid")}));return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return re.assign({},this.parsedResult)}satisfies(e){const t={};let n=0;const r={};let i=0;if(Object.keys(e).forEach((o=>{const a=e[o];"string"==typeof a?(r[o]=a,i+=1):"object"==typeof a&&(t[o]=a,n+=1)})),n>0){const e=Object.keys(t),n=re.find(e,(e=>this.isOS(e)));if(n){const e=this.satisfies(t[n]);if(void 0!==e)return e}const r=re.find(e,(e=>this.isPlatform(e)));if(r){const e=this.satisfies(t[r]);if(void 0!==e)return e}}if(i>0){const e=Object.keys(r),t=re.find(e,(e=>this.isBrowser(e,!0)));if(void 0!==t)return this.compareVersion(r[t])}}isBrowser(e,t=!1){const n=this.getBrowserName().toLowerCase();let r=e.toLowerCase();const i=re.getBrowserTypeByAlias(r);return t&&i&&(r=i.toLowerCase()),r===n}compareVersion(e){let t=[0],n=e,r=!1;const i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(n=e.substr(1),"="===e[1]?(r=!0,n=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?n=e.substr(1):"~"===e[0]&&(r=!0,n=e.substr(1)),t.indexOf(re.compareVersions(i,n,r))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,t=!1){return this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some((e=>this.is(e)))}}
/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class le{static getParser(e,t=!1){if("string"!=typeof e)throw new Error("UserAgent should be a string");return new ue(e,t)}static parse(e){return new ue(e).getResult()}static get BROWSER_MAP(){return Z}static get ENGINE_MAP(){return ne}static get OS_MAP(){return te}static get PLATFORMS_MAP(){return ee}}function de(){return Date.now()+Math.random().toString()}function he(){throw new Error("Method must be implemented in subclass")}function pe(e){return window._dailyConfig&&window._dailyConfig.proxyUrl?window._dailyConfig.proxyUrl+("/"===window._dailyConfig.proxyUrl.slice(-1)?"":"/")+e.substring(8):e}function fe(){return window._dailyConfig&&window._dailyConfig.callObjectBundleUrlOverride?window._dailyConfig.callObjectBundleUrlOverride:pe("https://c.daily.co/call-machine/versioned/".concat("0.55.1","/static/call-machine-object-bundle.js"))}function ve(e){try{new URL(e)}catch(e){return!1}return!0}const ge=Object.prototype.toString;function me(e){switch(ge.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return Ce(e,Error)}}function ye(e,t){return ge.call(e)===`[object ${t}]`}function _e(e){return ye(e,"ErrorEvent")}function be(e){return ye(e,"DOMError")}function we(e){return ye(e,"String")}function Se(e){return null===e||"object"!=typeof e&&"function"!=typeof e}function ke(e){return ye(e,"Object")}function Ee(e){return"undefined"!=typeof Event&&Ce(e,Event)}function Me(e){return Boolean(e&&e.then&&"function"==typeof e.then)}function Ce(e,t){try{return e instanceof t}catch(e){return!1}}function Te(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function Oe(e,t){if(!Array.isArray(e))return"";const n=[];for(let t=0;t<e.length;t++){const r=e[t];try{n.push(String(r))}catch(e){n.push("[value cannot be serialized]")}}return n.join(t)}function je(e,t,n=!1){return!!we(e)&&(ye(t,"RegExp")?t.test(e):!!we(t)&&(n?e===t:e.includes(t)))}function Pe(e,t=[],n=!1){return t.some((t=>je(e,t,n)))}function xe(e,t,n=250,r,i,o,a){if(!(o.exception&&o.exception.values&&a&&Ce(a.originalException,Error)))return;const s=o.exception.values.length>0?o.exception.values[o.exception.values.length-1]:void 0;var c,u;s&&(o.exception.values=(c=Ae(e,t,i,a.originalException,r,o.exception.values,s,0),u=n,c.map((e=>(e.value&&(e.value=Te(e.value,u)),e)))))}function Ae(e,t,n,r,i,o,a,s){if(o.length>=n+1)return o;let c=[...o];if(Ce(r[i],Error)){De(a,s);const o=e(t,r[i]),u=c.length;Le(o,i,u,s),c=Ae(e,t,n,r[i],i,[o,...c],o,u)}return Array.isArray(r.errors)&&r.errors.forEach(((r,o)=>{if(Ce(r,Error)){De(a,s);const u=e(t,r),l=c.length;Le(u,`errors[${o}]`,l,s),c=Ae(e,t,n,r,i,[u,...c],u,l)}})),c}function De(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,is_exception_group:!0,exception_id:t}}function Le(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function Ne(e){return e&&e.Math==Math?e:void 0}const Ie="object"==typeof globalThis&&Ne(globalThis)||"object"==typeof window&&Ne(window)||"object"==typeof self&&Ne(self)||"object"==typeof global&&Ne(global)||function(){return this}()||{};function Re(){return Ie}function Be(e,t,n){const r=n||Ie,i=r.__SENTRY__=r.__SENTRY__||{};return i[e]||(i[e]=t())}const Fe=Re(),Ue=80;function Ve(e,t={}){try{let n=e;const r=5,i=[];let o=0,a=0;const s=" > ",c=s.length;let u;const l=Array.isArray(t)?t:t.keyAttrs,d=!Array.isArray(t)&&t.maxStringLength||Ue;for(;n&&o++<r&&(u=$e(n,l),!("html"===u||o>1&&a+i.length*c+u.length>=d));)i.push(u),a+=u.length,n=n.parentNode;return i.reverse().join(s)}catch(e){return"<unknown>"}}function $e(e,t){const n=e,r=[];let i,o,a,s,c;if(!n||!n.tagName)return"";r.push(n.tagName.toLowerCase());const u=t&&t.length?t.filter((e=>n.getAttribute(e))).map((e=>[e,n.getAttribute(e)])):null;if(u&&u.length)u.forEach((e=>{r.push(`[${e[0]}="${e[1]}"]`)}));else if(n.id&&r.push(`#${n.id}`),i=n.className,i&&we(i))for(o=i.split(/\s+/),c=0;c<o.length;c++)r.push(`.${o[c]}`);const l=["aria-label","type","name","title","alt"];for(c=0;c<l.length;c++)a=l[c],s=n.getAttribute(a),s&&r.push(`[${a}="${s}"]`);return r.join("")}const Ge=["debug","info","warn","error","log","assert","trace"];function qe(e){if(!("console"in Ie))return e();const t=Ie.console,n={};Ge.forEach((e=>{const r=t[e]&&t[e].__sentry_original__;e in t&&r&&(n[e]=t[e],t[e]=r)}));try{return e()}finally{Object.keys(n).forEach((e=>{t[e]=n[e]}))}}function Ye(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1}};return"undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__?Ge.forEach((n=>{t[n]=(...t)=>{e&&qe((()=>{Ie.console[n](`Sentry Logger [${n}]:`,...t)}))}})):Ge.forEach((e=>{t[e]=()=>{}})),t}let ze;ze="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__?Be("logger",Ye):Ye();const Je=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function We(e,t=!1){const{host:n,path:r,pass:i,port:o,projectId:a,protocol:s,publicKey:c}=e;return`${s}://${c}${t&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r?`${r}/`:r}${a}`}function He(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function Ke(e){const t="string"==typeof e?function(e){const t=Je.exec(e);if(!t)return void console.error(`Invalid Sentry Dsn: ${e}`);const[n,r,i="",o,a="",s]=t.slice(1);let c="",u=s;const l=u.split("/");if(l.length>1&&(c=l.slice(0,-1).join("/"),u=l.pop()),u){const e=u.match(/^\d+/);e&&(u=e[0])}return He({host:o,pass:i,path:c,projectId:u,port:a,protocol:n,publicKey:r})}(e):He(e);if(t&&function(e){if("undefined"!=typeof __SENTRY_DEBUG__&&!__SENTRY_DEBUG__)return!0;const{port:t,projectId:n,protocol:r}=e;return!(["protocol","publicKey","host","projectId"].find((t=>!e[t]&&(ze.error(`Invalid Sentry Dsn: ${t} missing`),!0)))||(n.match(/^\d+$/)?function(e){return"http"===e||"https"===e}(r)?t&&isNaN(parseInt(t,10))&&(ze.error(`Invalid Sentry Dsn: Invalid port ${t}`),1):(ze.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),1):(ze.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),1)))}(t))return t}class Qe extends Error{constructor(e,t="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=t}}function Xe(e,t,n){if(!(t in e))return;const r=e[t],i=n(r);if("function"==typeof i)try{et(i,r)}catch(e){}e[t]=i}function Ze(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}function et(e,t){const n=t.prototype||{};e.prototype=t.prototype=n,Ze(e,"__sentry_original__",t)}function tt(e){return e.__sentry_original__}function nt(e){if(me(e))return{message:e.message,name:e.name,stack:e.stack,...it(e)};if(Ee(e)){const t={type:e.type,target:rt(e.target),currentTarget:rt(e.currentTarget),...it(e)};return"undefined"!=typeof CustomEvent&&Ce(e,CustomEvent)&&(t.detail=e.detail),t}return e}function rt(e){try{return t=e,"undefined"!=typeof Element&&Ce(t,Element)?Ve(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}var t}function it(e){if("object"==typeof e&&null!==e){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}return{}}function ot(e){return at(e,new Map)}function at(e,t){if(ke(e)){const n=t.get(e);if(void 0!==n)return n;const r={};t.set(e,r);for(const n of Object.keys(e))void 0!==e[n]&&(r[n]=at(e[n],t));return r}if(Array.isArray(e)){const n=t.get(e);if(void 0!==n)return n;const r=[];return t.set(e,r),e.forEach((e=>{r.push(at(e,t))})),r}return e}const st="<anonymous>";function ct(e){try{return e&&"function"==typeof e&&e.name||st}catch(e){return st}}const ut=Re();function lt(e){return e&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function dt(){if(!function(){if(!("fetch"in ut))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}())return!1;if(lt(ut.fetch))return!0;let e=!1;const t=ut.document;if(t&&"function"==typeof t.createElement)try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=lt(n.contentWindow.fetch)),t.head.removeChild(n)}catch(e){("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",e)}return e}const ht=Re();const pt=Re(),ft="__sentry_xhr_v2__",vt={},gt={};function mt(e){if(!gt[e])switch(gt[e]=!0,e){case"console":!function(){if(!("console"in pt))return;Ge.forEach((function(e){e in pt.console&&Xe(pt.console,e,(function(t){return function(...n){_t("console",{args:n,level:e}),t&&t.apply(pt.console,n)}}))}))}();break;case"dom":!function(){if(!("document"in pt))return;const e=_t.bind(null,"dom"),t=Ct(e,!0);pt.document.addEventListener("click",t,!1),pt.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach((t=>{const n=pt[t]&&pt[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(Xe(n,"addEventListener",(function(t){return function(n,r,i){if("click"===n||"keypress"==n)try{const r=this,o=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},a=o[n]=o[n]||{refCount:0};if(!a.handler){const r=Ct(e);a.handler=r,t.call(this,n,r,i)}a.refCount++}catch(e){}return t.call(this,n,r,i)}})),Xe(n,"removeEventListener",(function(e){return function(t,n,r){if("click"===t||"keypress"==t)try{const n=this,i=n.__sentry_instrumentation_handlers__||{},o=i[t];o&&(o.refCount--,o.refCount<=0&&(e.call(this,t,o.handler,r),o.handler=void 0,delete i[t]),0===Object.keys(i).length&&delete n.__sentry_instrumentation_handlers__)}catch(e){}return e.call(this,t,n,r)}})))}))}();break;case"xhr":!function(){if(!("XMLHttpRequest"in pt))return;const e=XMLHttpRequest.prototype;Xe(e,"open",(function(e){return function(...t){const n=t[1],r=this[ft]={method:we(t[0])?t[0].toUpperCase():t[0],url:t[1],request_headers:{}};we(n)&&"POST"===r.method&&n.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const i=()=>{const e=this[ft];if(e&&4===this.readyState){try{e.status_code=this.status}catch(e){}_t("xhr",{args:t,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:this})}};return"onreadystatechange"in this&&"function"==typeof this.onreadystatechange?Xe(this,"onreadystatechange",(function(e){return function(...t){return i(),e.apply(this,t)}})):this.addEventListener("readystatechange",i),Xe(this,"setRequestHeader",(function(e){return function(...t){const[n,r]=t,i=this[ft];return i&&(i.request_headers[n.toLowerCase()]=r),e.apply(this,t)}})),e.apply(this,t)}})),Xe(e,"send",(function(e){return function(...t){const n=this[ft];return n&&void 0!==t[0]&&(n.body=t[0]),_t("xhr",{args:t,startTimestamp:Date.now(),xhr:this}),e.apply(this,t)}}))}();break;case"fetch":!function(){if(!dt())return;Xe(pt,"fetch",(function(e){return function(...t){const{method:n,url:r}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){const[t,n]=e;return{url:wt(t),method:bt(n,"method")?String(n.method).toUpperCase():"GET"}}const t=e[0];return{url:wt(t),method:bt(t,"method")?String(t.method).toUpperCase():"GET"}}(t),i={args:t,fetchData:{method:n,url:r},startTimestamp:Date.now()};return _t("fetch",{...i}),e.apply(pt,t).then((e=>(_t("fetch",{...i,endTimestamp:Date.now(),response:e}),e)),(e=>{throw _t("fetch",{...i,endTimestamp:Date.now(),error:e}),e}))}}))}();break;case"history":!function(){if(!function(){const e=ht.chrome,t=e&&e.app&&e.app.runtime,n="history"in ht&&!!ht.history.pushState&&!!ht.history.replaceState;return!t&&n}())return;const e=pt.onpopstate;function t(e){return function(...t){const n=t.length>2?t[2]:void 0;if(n){const e=St,t=String(n);St=t,_t("history",{from:e,to:t})}return e.apply(this,t)}}pt.onpopstate=function(...t){const n=pt.location.href,r=St;if(St=n,_t("history",{from:r,to:n}),e)try{return e.apply(this,t)}catch(e){}},Xe(pt.history,"pushState",t),Xe(pt.history,"replaceState",t)}();break;case"error":Tt=pt.onerror,pt.onerror=function(e,t,n,r,i){return _t("error",{column:r,error:i,line:n,msg:e,url:t}),!(!Tt||Tt.__SENTRY_LOADER__)&&Tt.apply(this,arguments)},pt.onerror.__SENTRY_INSTRUMENTED__=!0;break;case"unhandledrejection":Ot=pt.onunhandledrejection,pt.onunhandledrejection=function(e){return _t("unhandledrejection",e),!(Ot&&!Ot.__SENTRY_LOADER__)||Ot.apply(this,arguments)},pt.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0;break;default:return void(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("unknown instrumentation type:",e))}}function yt(e,t){vt[e]=vt[e]||[],vt[e].push(t),mt(e)}function _t(e,t){if(e&&vt[e])for(const n of vt[e]||[])try{n(t)}catch(t){("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.error(`Error while triggering instrumentation handler.\nType: ${e}\nName: ${ct(n)}\nError:`,t)}}function bt(e,t){return!!e&&"object"==typeof e&&!!e[t]}function wt(e){return"string"==typeof e?e:e?bt(e,"url")?e.url:e.toString?e.toString():"":""}let St;const kt=1e3;let Et,Mt;function Ct(e,t=!1){return n=>{if(!n||Mt===n)return;if(function(e){if("keypress"!==e.type)return!1;try{const t=e.target;if(!t||!t.tagName)return!0;if("INPUT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable)return!1}catch(e){}return!0}(n))return;const r="keypress"===n.type?"input":n.type;(void 0===Et||function(e,t){if(!e)return!0;if(e.type!==t.type)return!0;try{if(e.target!==t.target)return!0}catch(e){}return!1}(Mt,n))&&(e({event:n,name:r,global:t}),Mt=n),clearTimeout(Et),Et=pt.setTimeout((()=>{Et=void 0}),kt)}}let Tt=null;let Ot=null;function jt(){const e=Ie,t=e.crypto||e.msCrypto;if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");const n=t&&t.getRandomValues?()=>t.getRandomValues(new Uint8Array(1))[0]:()=>16*Math.random();return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(e=>(e^(15&n())>>e/4).toString(16)))}function Pt(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function xt(e){const{message:t,event_id:n}=e;if(t)return t;const r=Pt(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function At(e,t,n){const r=e.exception=e.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{};o.value||(o.value=t||""),o.type||(o.type=n||"Error")}function Dt(e,t){const n=Pt(e);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){const e={...r&&r.data,...t.data};n.mechanism.data=e}}function Lt(e){if(e&&e.__sentry_captured__)return!0;try{Ze(e,"__sentry_captured__",!0)}catch(e){}return!1}function Nt(e,t=100,n=1/0){try{return Rt("",e,t,n)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function It(e,t=3,n=102400){const r=Nt(e,t);return i=r,function(e){return~-encodeURI(e).split(/%..|./).length}(JSON.stringify(i))>n?It(e,t-1,n):r;var i}function Rt(e,t,n=1/0,r=1/0,i=function(){const e="function"==typeof WeakSet,t=e?new WeakSet:[];return[function(n){if(e)return!!t.has(n)||(t.add(n),!1);for(let e=0;e<t.length;e++)if(t[e]===n)return!0;return t.push(n),!1},function(n){if(e)t.delete(n);else for(let e=0;e<t.length;e++)if(t[e]===n){t.splice(e,1);break}}]}()){const[o,a]=i;if(null==t||["number","boolean","string"].includes(typeof t)&&("number"!=typeof(s=t)||s==s))return t;var s;const c=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(function(e){return ke(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}(t))return"[SyntheticEvent]";if("number"==typeof t&&t!=t)return"[NaN]";if("function"==typeof t)return`[Function: ${ct(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;const n=function(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(e){return`**non-serializable** (${e})`}}(e,t);if(!c.startsWith("[object "))return c;if(t.__sentry_skip_normalization__)return t;const u="number"==typeof t.__sentry_override_normalization_depth__?t.__sentry_override_normalization_depth__:n;if(0===u)return c.replace("object ","");if(o(t))return"[Circular ~]";const l=t;if(l&&"function"==typeof l.toJSON)try{return Rt("",l.toJSON(),u-1,r,i)}catch(e){}const d=Array.isArray(t)?[]:{};let h=0;const p=nt(t);for(const e in p){if(!Object.prototype.hasOwnProperty.call(p,e))continue;if(h>=r){d[e]="[MaxProperties ~]";break}const t=p[e];d[e]=Rt(e,t,u-1,r,i),h++}return a(t),d}var Bt;function Ft(e){return new Vt((t=>{t(e)}))}function Ut(e){return new Vt(((t,n)=>{n(e)}))}!function(e){e[e.PENDING=0]="PENDING";e[e.RESOLVED=1]="RESOLVED";e[e.REJECTED=2]="REJECTED"}(Bt||(Bt={}));class Vt{__init(){this._state=Bt.PENDING}__init2(){this._handlers=[]}constructor(e){Vt.prototype.__init.call(this),Vt.prototype.__init2.call(this),Vt.prototype.__init3.call(this),Vt.prototype.__init4.call(this),Vt.prototype.__init5.call(this),Vt.prototype.__init6.call(this);try{e(this._resolve,this._reject)}catch(e){this._reject(e)}}then(e,t){return new Vt(((n,r)=>{this._handlers.push([!1,t=>{if(e)try{n(e(t))}catch(e){r(e)}else n(t)},e=>{if(t)try{n(t(e))}catch(e){r(e)}else r(e)}]),this._executeHandlers()}))}catch(e){return this.then((e=>e),e)}finally(e){return new Vt(((t,n)=>{let r,i;return this.then((t=>{i=!1,r=t,e&&e()}),(t=>{i=!0,r=t,e&&e()})).then((()=>{i?n(r):t(r)}))}))}__init3(){this._resolve=e=>{this._setResult(Bt.RESOLVED,e)}}__init4(){this._reject=e=>{this._setResult(Bt.REJECTED,e)}}__init5(){this._setResult=(e,t)=>{this._state===Bt.PENDING&&(Me(t)?t.then(this._resolve,this._reject):(this._state=e,this._value=t,this._executeHandlers()))}}__init6(){this._executeHandlers=()=>{if(this._state===Bt.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach((e=>{e[0]||(this._state===Bt.RESOLVED&&e[1](this._value),this._state===Bt.REJECTED&&e[2](this._value),e[0]=!0)}))}}}function $t(e){const t=[];function n(e){return t.splice(t.indexOf(e),1)[0]}return{$:t,add:function(r){if(!(void 0===e||t.length<e))return Ut(new Qe("Not adding Promise because buffer limit was reached."));const i=r();return-1===t.indexOf(i)&&t.push(i),i.then((()=>n(i))).then(null,(()=>n(i).then(null,(()=>{})))),i},drain:function(e){return new Vt(((n,r)=>{let i=t.length;if(!i)return n(!0);const o=setTimeout((()=>{e&&e>0&&n(!1)}),e);t.forEach((e=>{Ft(e).then((()=>{--i||(clearTimeout(o),n(!0))}),r)}))}))}}}function Gt(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}const qt=["fatal","error","warning","log","info","debug"];const Yt=Re(),zt={nowSeconds:()=>Date.now()/1e3};const Jt="undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&__SENTRY_BROWSER_BUNDLE__||"[object process]"!==Object.prototype.toString.call("undefined"!=typeof process?process:0)?function(){const{performance:e}=Yt;if(!e||!e.now)return;return{now:()=>e.now(),timeOrigin:Date.now()-e.now()}}():function(){try{return(e=module,t="perf_hooks",e.require(t)).performance}catch(e){return}var e,t}(),Wt=void 0===Jt?zt:{nowSeconds:()=>(Jt.timeOrigin+Jt.now())/1e3},Ht=zt.nowSeconds.bind(zt),Kt=Wt.nowSeconds.bind(Wt);function Qt(e,t=[]){return[e,t]}function Xt(e,t){const[n,r]=e;return[n,[...r,t]]}function Zt(e,t){const n=e[1];for(const e of n){if(t(e,e[0].type))return!0}return!1}function en(e,t){return(t||new TextEncoder).encode(e)}function tn(e,t){const[n,r]=e;let i=JSON.stringify(n);function o(e){"string"==typeof i?i="string"==typeof e?i+e:[en(i,t),e]:i.push("string"==typeof e?en(e,t):e)}for(const e of r){const[t,n]=e;if(o(`\n${JSON.stringify(t)}\n`),"string"==typeof n||n instanceof Uint8Array)o(n);else{let e;try{e=JSON.stringify(n)}catch(t){e=JSON.stringify(Nt(n))}o(e)}}return"string"==typeof i?i:function(e){const t=e.reduce(((e,t)=>e+t.length),0),n=new Uint8Array(t);let r=0;for(const t of e)n.set(t,r),r+=t.length;return n}(i)}function nn(e,t){const n="string"==typeof e.data?en(e.data,t):e.data;return[ot({type:"attachment",length:n.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),n]}(()=>{const{performance:e}=Yt;if(!e||!e.now)return;const t=36e5,n=e.now(),r=Date.now(),i=e.timeOrigin?Math.abs(e.timeOrigin+n-r):t,o=i<t,a=e.timing&&e.timing.navigationStart,s="number"==typeof a?Math.abs(a+n-r):t;(o||s<t)&&(i<=s&&e.timeOrigin)})();const rn={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor"};function on(e){return rn[e]}function an(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}const sn=6e4;function cn(e,{statusCode:t,headers:n},r=Date.now()){const i={...e},o=n&&n["x-sentry-rate-limits"],a=n&&n["retry-after"];if(o)for(const e of o.trim().split(",")){const[t,n]=e.split(":",2),o=parseInt(t,10),a=1e3*(isNaN(o)?60:o);if(n)for(const e of n.split(";"))i[e]=r+a;else i.all=r+a}else a?i.all=r+function(e,t=Date.now()){const n=parseInt(`${e}`,10);if(!isNaN(n))return 1e3*n;const r=Date.parse(`${e}`);return isNaN(r)?sn:r-t}(a,r):429===t&&(i.all=r+6e4);return i}const un="production";function ln(e){const t=Kt(),n={sid:jt(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(e){return ot({sid:`${e.sid}`,init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"==typeof e.did||"string"==typeof e.did?`${e.did}`:void 0,duration:e.duration,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}(n)};return e&&dn(n,e),n}function dn(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||Kt(),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:jt()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{const t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}class hn{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=vn()}static clone(e){const t=new hn;return e&&(t._breadcrumbs=[...e._breadcrumbs],t._tags={...e._tags},t._extra={...e._extra},t._contexts={...e._contexts},t._user=e._user,t._level=e._level,t._span=e._span,t._session=e._session,t._transactionName=e._transactionName,t._fingerprint=e._fingerprint,t._eventProcessors=[...e._eventProcessors],t._requestSession=e._requestSession,t._attachments=[...e._attachments],t._sdkProcessingMetadata={...e._sdkProcessingMetadata},t._propagationContext={...e._propagationContext}),t}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{},this._session&&dn(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSpan(e){return this._span=e,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){const e=this.getSpan();return e&&e.transaction}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;if("function"==typeof e){const t=e(this);return t instanceof hn?t:this}return e instanceof hn?(this._tags={...this._tags,...e._tags},this._extra={...this._extra,...e._extra},this._contexts={...this._contexts,...e._contexts},e._user&&Object.keys(e._user).length&&(this._user=e._user),e._level&&(this._level=e._level),e._fingerprint&&(this._fingerprint=e._fingerprint),e._requestSession&&(this._requestSession=e._requestSession),e._propagationContext&&(this._propagationContext=e._propagationContext)):ke(e)&&(this._tags={...this._tags,...e.tags},this._extra={...this._extra,...e.extra},this._contexts={...this._contexts,...e.contexts},e.user&&(this._user=e.user),e.level&&(this._level=e.level),e.fingerprint&&(this._fingerprint=e.fingerprint),e.requestSession&&(this._requestSession=e.requestSession),e.propagationContext&&(this._propagationContext=e.propagationContext)),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=vn(),this}addBreadcrumb(e,t){const n="number"==typeof t?t:100;if(n<=0)return this;const r={timestamp:Ht(),...e};return this._breadcrumbs=[...this._breadcrumbs,r].slice(-n),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}getAttachments(){return this._attachments}clearAttachments(){return this._attachments=[],this}applyToEvent(e,t={}){if(this._extra&&Object.keys(this._extra).length&&(e.extra={...this._extra,...e.extra}),this._tags&&Object.keys(this._tags).length&&(e.tags={...this._tags,...e.tags}),this._user&&Object.keys(this._user).length&&(e.user={...this._user,...e.user}),this._contexts&&Object.keys(this._contexts).length&&(e.contexts={...this._contexts,...e.contexts}),this._level&&(e.level=this._level),this._transactionName&&(e.transaction=this._transactionName),this._span){e.contexts={trace:this._span.getTraceContext(),...e.contexts};const t=this._span.transaction;if(t){e.sdkProcessingMetadata={dynamicSamplingContext:t.getDynamicSamplingContext(),...e.sdkProcessingMetadata};const n=t.name;n&&(e.tags={transaction:n,...e.tags})}}return this._applyFingerprint(e),e.breadcrumbs=[...e.breadcrumbs||[],...this._breadcrumbs],e.breadcrumbs=e.breadcrumbs.length>0?e.breadcrumbs:void 0,e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...this._sdkProcessingMetadata,propagationContext:this._propagationContext},this._notifyEventProcessors([...pn(),...this._eventProcessors],e,t)}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...e},this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}_notifyEventProcessors(e,t,n,r=0){return new Vt(((i,o)=>{const a=e[r];if(null===t||"function"!=typeof a)i(t);else{const s=a({...t},n);("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&a.id&&null===s&&ze.log(`Event processor "${a.id}" dropped event`),Me(s)?s.then((t=>this._notifyEventProcessors(e,t,n,r+1).then(i))).then(null,o):this._notifyEventProcessors(e,s,n,r+1).then(i).then(null,o)}}))}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((e=>{e(this)})),this._notifyingListeners=!1)}_applyFingerprint(e){var t;e.fingerprint=e.fingerprint?(t=e.fingerprint,Array.isArray(t)?t:[t]):[],this._fingerprint&&(e.fingerprint=e.fingerprint.concat(this._fingerprint)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}}function pn(){return Be("globalEventProcessors",(()=>[]))}function fn(e){pn().push(e)}function vn(){return{traceId:jt(),spanId:jt().substring(16),sampled:!1}}const gn=4,mn=100;class yn{constructor(e,t=new hn,n=gn){this._version=n,this._stack=[{scope:t}],e&&this.bindClient(e)}isOlderThan(e){return this._version<e}bindClient(e){this.getStackTop().client=e,e&&e.setupIntegrations&&e.setupIntegrations()}pushScope(){const e=hn.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:e}),e}popScope(){return!(this.getStack().length<=1)&&!!this.getStack().pop()}withScope(e){const t=this.pushScope();try{e(t)}finally{this.popScope()}}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(e,t){const n=this._lastEventId=t&&t.event_id?t.event_id:jt(),r=new Error("Sentry syntheticException");return this._withClient(((i,o)=>{i.captureException(e,{originalException:e,syntheticException:r,...t,event_id:n},o)})),n}captureMessage(e,t,n){const r=this._lastEventId=n&&n.event_id?n.event_id:jt(),i=new Error(e);return this._withClient(((o,a)=>{o.captureMessage(e,t,{originalException:e,syntheticException:i,...n,event_id:r},a)})),r}captureEvent(e,t){const n=t&&t.event_id?t.event_id:jt();return e.type||(this._lastEventId=n),this._withClient(((r,i)=>{r.captureEvent(e,{...t,event_id:n},i)})),n}lastEventId(){return this._lastEventId}addBreadcrumb(e,t){const{scope:n,client:r}=this.getStackTop();if(!r)return;const{beforeBreadcrumb:i=null,maxBreadcrumbs:o=mn}=r.getOptions&&r.getOptions()||{};if(o<=0)return;const a={timestamp:Ht(),...e},s=i?qe((()=>i(a,t))):a;null!==s&&(r.emit&&r.emit("beforeAddBreadcrumb",s,t),n.addBreadcrumb(s,o))}setUser(e){this.getScope().setUser(e)}setTags(e){this.getScope().setTags(e)}setExtras(e){this.getScope().setExtras(e)}setTag(e,t){this.getScope().setTag(e,t)}setExtra(e,t){this.getScope().setExtra(e,t)}setContext(e,t){this.getScope().setContext(e,t)}configureScope(e){const{scope:t,client:n}=this.getStackTop();n&&e(t)}run(e){const t=bn(this);try{e(this)}finally{bn(t)}}getIntegration(e){const t=this.getClient();if(!t)return null;try{return t.getIntegration(e)}catch(t){return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Cannot retrieve integration ${e.id} from the current Hub`),null}}startTransaction(e,t){const n=this._callExtensionMethod("startTransaction",e,t);return"undefined"!=typeof __SENTRY_DEBUG__&&!__SENTRY_DEBUG__||n||console.warn("Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':\nSentry.addTracingExtensions();\nSentry.init({...});\n"),n}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(e=!1){if(e)return this.endSession();this._sendSessionUpdate()}endSession(){const e=this.getStackTop().scope,t=e.getSession();t&&function(e,t){let n={};t?n={status:t}:"ok"===e.status&&(n={status:"exited"}),dn(e,n)}(t),this._sendSessionUpdate(),e.setSession()}startSession(e){const{scope:t,client:n}=this.getStackTop(),{release:r,environment:i=un}=n&&n.getOptions()||{},{userAgent:o}=Ie.navigator||{},a=ln({release:r,environment:i,user:t.getUser(),...o&&{userAgent:o},...e}),s=t.getSession&&t.getSession();return s&&"ok"===s.status&&dn(s,{status:"exited"}),this.endSession(),t.setSession(a),a}shouldSendDefaultPii(){const e=this.getClient(),t=e&&e.getOptions();return Boolean(t&&t.sendDefaultPii)}_sendSessionUpdate(){const{scope:e,client:t}=this.getStackTop(),n=e.getSession();n&&t&&t.captureSession&&t.captureSession(n)}_withClient(e){const{scope:t,client:n}=this.getStackTop();n&&e(n,t)}_callExtensionMethod(e,...t){const n=_n().__SENTRY__;if(n&&n.extensions&&"function"==typeof n.extensions[e])return n.extensions[e].apply(this,t);("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Extension method ${e} couldn't be found, doing nothing.`)}}function _n(){return Ie.__SENTRY__=Ie.__SENTRY__||{extensions:{},hub:void 0},Ie}function bn(e){const t=_n(),n=Sn(t);return kn(t,e),n}function wn(){const e=_n();if(e.__SENTRY__&&e.__SENTRY__.acs){const t=e.__SENTRY__.acs.getCurrentHub();if(t)return t}return function(e=_n()){t=e,t&&t.__SENTRY__&&t.__SENTRY__.hub&&!Sn(e).isOlderThan(gn)||kn(e,new yn);var t;return Sn(e)}(e)}function Sn(e){return Be("hub",(()=>new yn),e)}function kn(e,t){if(!e)return!1;return(e.__SENTRY__=e.__SENTRY__||{}).hub=t,!0}const En="7";function Mn(e,t){return n={sentry_key:e.publicKey,sentry_version:En,...t&&{sentry_client:`${t.name}/${t.version}`}},Object.keys(n).map((e=>`${encodeURIComponent(e)}=${encodeURIComponent(n[e])}`)).join("&");var n}function Cn(e,t,n,r){const i=an(n),o=e.type&&"replay_event"!==e.type?e.type:"event";!function(e,t){t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]])}(e,n&&n.sdk);const a=function(e,t,n,r){const i=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&{sdk:t},...!!n&&{dsn:We(r)},...i&&{trace:ot({...i})}}}(e,i,r,t);delete e.sdkProcessingMetadata;return Qt(a,[[{type:o},e]])}const Tn=[];function On(e,t){t[e.name]=e,-1===Tn.indexOf(e.name)&&(e.setupOnce(fn,wn),Tn.push(e.name),("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log(`Integration installed: ${e.name}`))}function jn(e,t,n,r){const{normalizeDepth:i=3,normalizeMaxBreadth:o=1e3}=e,a={...t,event_id:t.event_id||n.event_id||jt(),timestamp:t.timestamp||Ht()},s=n.integrations||e.integrations.map((e=>e.name));!function(e,t){const{environment:n,release:r,dist:i,maxValueLength:o=250}=t;"environment"in e||(e.environment="environment"in t?n:un);void 0===e.release&&void 0!==r&&(e.release=r);void 0===e.dist&&void 0!==i&&(e.dist=i);e.message&&(e.message=Te(e.message,o));const a=e.exception&&e.exception.values&&e.exception.values[0];a&&a.value&&(a.value=Te(a.value,o));const s=e.request;s&&s.url&&(s.url=Te(s.url,o))}(a,e),function(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}(a,s),void 0===t.type&&function(e,t){const n=Ie._sentryDebugIds;if(!n)return;let r;const i=Pn.get(t);i?r=i:(r=new Map,Pn.set(t,r));const o=Object.keys(n).reduce(((e,i)=>{let o;const a=r.get(i);a?o=a:(o=t(i),r.set(i,o));for(let t=o.length-1;t>=0;t--){const r=o[t];if(r.filename){e[r.filename]=n[i];break}}return e}),{});try{e.exception.values.forEach((e=>{e.stacktrace.frames.forEach((e=>{e.filename&&(e.debug_id=o[e.filename])}))}))}catch(e){}}(a,e.stackParser);let c=r;n.captureContext&&(c=hn.clone(c).update(n.captureContext));let u=Ft(a);if(c){if(c.getAttachments){const e=[...n.attachments||[],...c.getAttachments()];e.length&&(n.attachments=e)}u=c.applyToEvent(a,n)}return u.then((e=>(e&&function(e){const t={};try{e.exception.values.forEach((e=>{e.stacktrace.frames.forEach((e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)}))}))}catch(e){}if(0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.keys(t).forEach((e=>{n.push({type:"sourcemap",code_file:e,debug_id:t[e]})}))}(e),"number"==typeof i&&i>0?function(e,t,n){if(!e)return null;const r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map((e=>({...e,...e.data&&{data:Nt(e.data,t,n)}})))},...e.user&&{user:Nt(e.user,t,n)},...e.contexts&&{contexts:Nt(e.contexts,t,n)},...e.extra&&{extra:Nt(e.extra,t,n)}};e.contexts&&e.contexts.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=Nt(e.contexts.trace.data,t,n)));e.spans&&(r.spans=e.spans.map((e=>(e.data&&(e.data=Nt(e.data,t,n)),e))));return r}(e,i,o):e)))}const Pn=new WeakMap;const xn="Not capturing exception because it's already been captured.";class An{__init(){this._integrations={}}__init2(){this._integrationsInitialized=!1}__init3(){this._numProcessing=0}__init4(){this._outcomes={}}__init5(){this._hooks={}}constructor(e){if(An.prototype.__init.call(this),An.prototype.__init2.call(this),An.prototype.__init3.call(this),An.prototype.__init4.call(this),An.prototype.__init5.call(this),this._options=e,e.dsn?this._dsn=Ke(e.dsn):("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("No DSN provided, client will not do anything."),this._dsn){const t=function(e,t={}){const n="string"==typeof t?t:t.tunnel,r="string"!=typeof t&&t._metadata?t._metadata.sdk:void 0;return n||`${function(e){return`${function(e){const t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/`}(e)}?${Mn(e,r)}`}(this._dsn,e);this._transport=e.transport({recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}}captureException(e,t,n){if(Lt(e))return void(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log(xn));let r=t&&t.event_id;return this._process(this.eventFromException(e,t).then((e=>this._captureEvent(e,t,n))).then((e=>{r=e}))),r}captureMessage(e,t,n,r){let i=n&&n.event_id;const o=Se(e)?this.eventFromMessage(String(e),t,n):this.eventFromException(e,n);return this._process(o.then((e=>this._captureEvent(e,n,r))).then((e=>{i=e}))),i}captureEvent(e,t,n){if(t&&t.originalException&&Lt(t.originalException))return void(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log(xn));let r=t&&t.event_id;return this._process(this._captureEvent(e,t,n).then((e=>{r=e}))),r}captureSession(e){this._isEnabled()?"string"!=typeof e.release?("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),dn(e,{init:!1})):("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("SDK not enabled, will not capture session.")}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const t=this._transport;return t?this._isClientDoneProcessing(e).then((n=>t.flush(e).then((e=>n&&e)))):Ft(!0)}close(e){return this.flush(e).then((e=>(this.getOptions().enabled=!1,e)))}setupIntegrations(){this._isEnabled()&&!this._integrationsInitialized&&(this._integrations=function(e){const t={};return e.forEach((e=>{e&&On(e,t)})),t}(this._options.integrations),this._integrationsInitialized=!0)}getIntegrationById(e){return this._integrations[e]}getIntegration(e){try{return this._integrations[e.id]||null}catch(t){return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Cannot retrieve integration ${e.id} from the current Client`),null}}addIntegration(e){On(e,this._integrations)}sendEvent(e,t={}){if(this._dsn){let n=Cn(e,this._dsn,this._options._metadata,this._options.tunnel);for(const e of t.attachments||[])n=Xt(n,nn(e,this._options.transportOptions&&this._options.transportOptions.textEncoder));const r=this._sendEnvelope(n);r&&r.then((t=>this.emit("afterSendEvent",e,t)),null)}}sendSession(e){if(this._dsn){const t=function(e,t,n,r){const i=an(n);return Qt({sent_at:(new Date).toISOString(),...i&&{sdk:i},...!!r&&{dsn:We(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(t)}}recordDroppedEvent(e,t,n){if(this._options.sendClientReports){const n=`${e}:${t}`;("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log(`Adding outcome: "${n}"`),this._outcomes[n]=this._outcomes[n]+1||1}}on(e,t){this._hooks[e]||(this._hooks[e]=[]),this._hooks[e].push(t)}emit(e,...t){this._hooks[e]&&this._hooks[e].forEach((e=>e(...t)))}_updateSessionFromEvent(e,t){let n=!1,r=!1;const i=t.exception&&t.exception.values;if(i){r=!0;for(const e of i){const t=e.mechanism;if(t&&!1===t.handled){n=!0;break}}}const o="ok"===e.status;(o&&0===e.errors||o&&n)&&(dn(e,{...n&&{status:"crashed"},errors:e.errors||Number(r||n)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Vt((t=>{let n=0;const r=setInterval((()=>{0==this._numProcessing?(clearInterval(r),t(!0)):(n+=1,e&&n>=e&&(clearInterval(r),t(!1)))}),1)}))}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._dsn}_prepareEvent(e,t,n){const r=this.getOptions(),i=Object.keys(this._integrations);return!t.integrations&&i.length>0&&(t.integrations=i),jn(r,e,t,n).then((e=>{if(null===e)return e;const{propagationContext:t}=e.sdkProcessingMetadata||{};if(!(e.contexts&&e.contexts.trace)&&t){const{traceId:r,spanId:i,parentSpanId:o,dsc:a}=t;e.contexts={trace:{trace_id:r,span_id:i,parent_span_id:o},...e.contexts};const s=a||function(e,t,n){const r=t.getOptions(),{publicKey:i}=t.getDsn()||{},{segment:o}=n&&n.getUser()||{},a=ot({environment:r.environment||un,release:r.release,user_segment:o,public_key:i,trace_id:e});return t.emit&&t.emit("createDsc",a),a}(r,this,n);e.sdkProcessingMetadata={dynamicSamplingContext:s,...e.sdkProcessingMetadata}}return e}))}_captureEvent(e,t={},n){return this._processEvent(e,t,n).then((e=>e.event_id),(e=>{if("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__){const t=e;"log"===t.logLevel?ze.log(t.message):ze.warn(t)}}))}_processEvent(e,t,n){const r=this.getOptions(),{sampleRate:i}=r;if(!this._isEnabled())return Ut(new Qe("SDK not enabled, will not capture event.","log"));const o=Ln(e),a=Dn(e),s=e.type||"error",c=`before send for type \`${s}\``;if(a&&"number"==typeof i&&Math.random()>i)return this.recordDroppedEvent("sample_rate","error",e),Ut(new Qe(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));const u="replay_event"===s?"replay":s;return this._prepareEvent(e,t,n).then((n=>{if(null===n)throw this.recordDroppedEvent("event_processor",u,e),new Qe("An event processor returned `null`, will not send event.","log");if(t.data&&!0===t.data.__sentry__)return n;const i=function(e,t,n){const{beforeSend:r,beforeSendTransaction:i}=e;if(Dn(t)&&r)return r(t,n);if(Ln(t)&&i)return i(t,n);return t}(r,n,t);return function(e,t){const n=`${t} must return \`null\` or a valid event.`;if(Me(e))return e.then((e=>{if(!ke(e)&&null!==e)throw new Qe(n);return e}),(e=>{throw new Qe(`${t} rejected with ${e}`)}));if(!ke(e)&&null!==e)throw new Qe(n);return e}(i,c)})).then((r=>{if(null===r)throw this.recordDroppedEvent("before_send",u,e),new Qe(`${c} returned \`null\`, will not send event.`,"log");const i=n&&n.getSession();!o&&i&&this._updateSessionFromEvent(i,r);const a=r.transaction_info;if(o&&a&&r.transaction!==e.transaction){const e="custom";r.transaction_info={...a,source:e}}return this.sendEvent(r,t),r})).then(null,(e=>{if(e instanceof Qe)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),new Qe(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${e}`)}))}_process(e){this._numProcessing++,e.then((e=>(this._numProcessing--,e)),(e=>(this._numProcessing--,e)))}_sendEnvelope(e){if(this._transport&&this._dsn)return this.emit("beforeEnvelope",e),this._transport.send(e).then(null,(e=>{("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.error("Error while sending event:",e)}));("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.error("Transport disabled")}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.keys(e).map((t=>{const[n,r]=t.split(":");return{reason:n,category:r,quantity:e[t]}}))}}function Dn(e){return void 0===e.type}function Ln(e){return"transaction"===e.type}const Nn=30;function In(e,t,n=$t(e.bufferSize||Nn)){let r={};function i(i){const o=[];if(Zt(i,((t,n)=>{const i=on(n);if(function(e,t,n=Date.now()){return function(e,t){return e[t]||e.all||0}(e,t)>n}(r,i)){const r=Rn(t,n);e.recordDroppedEvent("ratelimit_backoff",i,r)}else o.push(t)})),0===o.length)return Ft();const a=Qt(i[0],o),s=t=>{Zt(a,((n,r)=>{const i=Rn(n,r);e.recordDroppedEvent(t,on(r),i)}))};return n.add((()=>t({body:tn(a,e.textEncoder)}).then((e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Sentry responded with status code ${e.statusCode} to sent event.`),r=cn(r,e),e)),(e=>{throw s("network_error"),e})))).then((e=>e),(e=>{if(e instanceof Qe)return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.error("Skipped sending event because buffer is full."),s("queue_overflow"),Ft();throw e}))}return i.__sentry__baseTransport__=!0,{send:i,flush:e=>n.drain(e)}}function Rn(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}const Bn="7.60.1";let Fn;class Un{constructor(){Un.prototype.__init.call(this)}static __initStatic(){this.id="FunctionToString"}__init(){this.name=Un.id}setupOnce(){Fn=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=tt(this)||this;return Fn.apply(t,e)}}catch(e){}}}Un.__initStatic();const Vn=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],$n=[/^.*healthcheck.*$/,/^.*healthy.*$/,/^.*live.*$/,/^.*ready.*$/,/^.*heartbeat.*$/,/^.*\/health$/,/^.*\/healthz$/];class Gn{static __initStatic(){this.id="InboundFilters"}__init(){this.name=Gn.id}constructor(e={}){this._options=e,Gn.prototype.__init.call(this)}setupOnce(e,t){const n=e=>{const n=t();if(n){const t=n.getIntegration(Gn);if(t){const r=n.getClient(),i=r?r.getOptions():{},o=function(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:Vn],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[],...e.disableTransactionDefaults?[]:$n],ignoreInternal:void 0===e.ignoreInternal||e.ignoreInternal}}(t._options,i);return function(e,t){if(t.ignoreInternal&&function(e){try{return"SentryError"===e.exception.values[0].type}catch(e){}return!1}(e))return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Event dropped due to being internal Sentry Error.\nEvent: ${xt(e)}`),!0;if(function(e,t){if(e.type||!t||!t.length)return!1;return function(e){if(e.message)return[e.message];if(e.exception){const{values:t}=e.exception;try{const{type:e="",value:n=""}=t&&t[t.length-1]||{};return[`${n}`,`${e}: ${n}`]}catch(t){return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.error(`Cannot extract message for event ${xt(e)}`),[]}}return[]}(e).some((e=>Pe(e,t)))}(e,t.ignoreErrors))return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Event dropped due to being matched by \`ignoreErrors\` option.\nEvent: ${xt(e)}`),!0;if(function(e,t){if("transaction"!==e.type||!t||!t.length)return!1;const n=e.transaction;return!!n&&Pe(n,t)}(e,t.ignoreTransactions))return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.\nEvent: ${xt(e)}`),!0;if(function(e,t){if(!t||!t.length)return!1;const n=qn(e);return!!n&&Pe(n,t)}(e,t.denyUrls))return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Event dropped due to being matched by \`denyUrls\` option.\nEvent: ${xt(e)}.\nUrl: ${qn(e)}`),!0;if(!function(e,t){if(!t||!t.length)return!0;const n=qn(e);return!n||Pe(n,t)}(e,t.allowUrls))return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`Event dropped due to not being matched by \`allowUrls\` option.\nEvent: ${xt(e)}.\nUrl: ${qn(e)}`),!0;return!1}(e,o)?null:e}}return e};n.id=this.name,e(n)}}function qn(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch(e){}return t?function(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(t):null}catch(t){return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.error(`Cannot extract url for event ${xt(e)}`),null}}Gn.__initStatic();var Yn=Object.freeze({__proto__:null,FunctionToString:Un,InboundFilters:Gn});const zn=Ie;let Jn=0;function Wn(){return Jn>0}function Hn(e,t={},n){if("function"!=typeof e)return e;try{const t=e.__sentry_wrapped__;if(t)return t;if(tt(e))return e}catch(t){return e}const r=function(){const r=Array.prototype.slice.call(arguments);try{n&&"function"==typeof n&&n.apply(this,arguments);const i=r.map((e=>Hn(e,t)));return e.apply(this,i)}catch(e){throw Jn++,setTimeout((()=>{Jn--})),i=n=>{var i,o;n.addEventProcessor((e=>(t.mechanism&&(At(e,void 0,void 0),Dt(e,t.mechanism)),e.extra={...e.extra,arguments:r},e))),i=e,wn().captureException(i,{captureContext:o})},wn().withScope(i),e}var i};try{for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}catch(e){}et(r,e),Ze(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>e.name})}catch(e){}return r}function Kn(e,t){const n=Xn(e,t),r={type:t&&t.name,value:er(t)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function Qn(e,t){return{exception:{values:[Kn(e,t)]}}}function Xn(e,t){const n=t.stacktrace||t.stack||"",r=function(e){if(e){if("number"==typeof e.framesToPop)return e.framesToPop;if(Zn.test(e.message))return 1}return 0}(t);try{return e(n,r)}catch(e){}return[]}const Zn=/Minified React error #\d+;/i;function er(e){const t=e&&e.message;return t?t.error&&"string"==typeof t.error.message?t.error.message:t:"No error message"}function tr(e,t,n,r,i){let o;if(_e(t)&&t.error){return Qn(e,t.error)}if(be(t)||ye(t,"DOMException")){const i=t;if("stack"in t)o=Qn(e,t);else{const t=i.name||(be(i)?"DOMError":"DOMException"),a=i.message?`${t}: ${i.message}`:t;o=nr(e,a,n,r),At(o,a)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}if(me(t))return Qn(e,t);if(ke(t)||Ee(t)){return o=function(e,t,n,r){const i=wn().getClient(),o=i&&i.getOptions().normalizeDepth,a={exception:{values:[{type:Ee(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:rr(t,{isUnhandledRejection:r})}]},extra:{__serialized__:It(t,o)}};if(n){const t=Xn(e,n);t.length&&(a.exception.values[0].stacktrace={frames:t})}return a}(e,t,n,i),Dt(o,{synthetic:!0}),o}return o=nr(e,t,n,r),At(o,`${t}`,void 0),Dt(o,{synthetic:!0}),o}function nr(e,t,n,r){const i={message:t};if(r&&n){const r=Xn(e,n);r.length&&(i.exception={values:[{value:t,stacktrace:{frames:r}}]})}return i}function rr(e,{isUnhandledRejection:t}){const n=function(e,t=40){const n=Object.keys(nt(e));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=t)return Te(n[0],t);for(let e=n.length;e>0;e--){const r=n.slice(0,e).join(", ");if(!(r.length>t))return e===n.length?r:Te(r,t)}return""}(e),r=t?"promise rejection":"exception";if(_e(e))return`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``;if(Ee(e)){return`Event \`${function(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e)}\` (type=${e.type}) captured as ${r}`}return`Object captured as ${r} with keys: ${n}`}const ir=1024,or="Breadcrumbs";class ar{static __initStatic(){this.id=or}__init(){this.name=ar.id}constructor(e){ar.prototype.__init.call(this),this.options={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e}}setupOnce(){this.options.console&&yt("console",sr),this.options.dom&&yt("dom",function(e){function t(t){let n,r="object"==typeof e?e.serializeAttribute:void 0,i="object"==typeof e&&"number"==typeof e.maxStringLength?e.maxStringLength:void 0;i&&i>ir&&(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${i} was configured. Sentry will use 1024 instead.`),i=ir),"string"==typeof r&&(r=[r]);try{const e=t.event;n=function(e){return!!e&&!!e.target}(e)?Ve(e.target,{keyAttrs:r,maxStringLength:i}):Ve(e,{keyAttrs:r,maxStringLength:i})}catch(e){n="<unknown>"}0!==n.length&&wn().addBreadcrumb({category:`ui.${t.name}`,message:n},{event:t.event,name:t.name,global:t.global})}return t}(this.options.dom)),this.options.xhr&&yt("xhr",cr),this.options.fetch&&yt("fetch",ur),this.options.history&&yt("history",lr)}addSentryBreadcrumb(e){this.options.sentry&&wn().addBreadcrumb({category:"sentry."+("transaction"===e.type?"transaction":"event"),event_id:e.event_id,level:e.level,message:xt(e)},{event:e})}}function sr(e){for(let t=0;t<e.args.length;t++)if("ref=Ref<"===e.args[t]){e.args[t+1]="viewRef";break}const t={category:"console",data:{arguments:e.args,logger:"console"},level:(n=e.level,"warn"===n?"warning":qt.includes(n)?n:"log"),message:Oe(e.args," ")};var n;if("assert"===e.level){if(!1!==e.args[0])return;t.message=`Assertion failed: ${Oe(e.args.slice(1)," ")||"console.assert"}`,t.data.arguments=e.args.slice(1)}wn().addBreadcrumb(t,{input:e.args,level:e.level})}function cr(e){const{startTimestamp:t,endTimestamp:n}=e,r=e.xhr[ft];if(!t||!n||!r)return;const{method:i,url:o,status_code:a,body:s}=r,c={method:i,url:o,status_code:a},u={xhr:e.xhr,input:s,startTimestamp:t,endTimestamp:n};wn().addBreadcrumb({category:"xhr",data:c,type:"http"},u)}function ur(e){const{startTimestamp:t,endTimestamp:n}=e;if(n&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.error){const r=e.fetchData,i={data:e.error,input:e.args,startTimestamp:t,endTimestamp:n};wn().addBreadcrumb({category:"fetch",data:r,level:"error",type:"http"},i)}else{const r={...e.fetchData,status_code:e.response&&e.response.status},i={input:e.args,response:e.response,startTimestamp:t,endTimestamp:n};wn().addBreadcrumb({category:"fetch",data:r,type:"http"},i)}}function lr(e){let t=e.from,n=e.to;const r=Gt(zn.location.href);let i=Gt(t);const o=Gt(n);i.path||(i=r),r.protocol===o.protocol&&r.host===o.host&&(n=o.relative),r.protocol===i.protocol&&r.host===i.host&&(t=i.relative),wn().addBreadcrumb({category:"navigation",data:{from:t,to:n}})}ar.__initStatic();class dr extends An{constructor(e){const t=zn.SENTRY_SDK_SOURCE||"npm";e._metadata=e._metadata||{},e._metadata.sdk=e._metadata.sdk||{name:"sentry.javascript.browser",packages:[{name:`${t}:@sentry/browser`,version:Bn}],version:Bn},super(e),e.sendClientReports&&zn.document&&zn.document.addEventListener("visibilitychange",(()=>{"hidden"===zn.document.visibilityState&&this._flushOutcomes()}))}eventFromException(e,t){return function(e,t,n,r){const i=tr(e,t,n&&n.syntheticException||void 0,r);return Dt(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),Ft(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",n){return function(e,t,n="info",r,i){const o=nr(e,t,r&&r.syntheticException||void 0,i);return o.level=n,r&&r.event_id&&(o.event_id=r.event_id),Ft(o)}(this._options.stackParser,e,t,n,this._options.attachStacktrace)}sendEvent(e,t){const n=this.getIntegrationById(or);n&&n.addSentryBreadcrumb&&n.addSentryBreadcrumb(e),super.sendEvent(e,t)}captureUserFeedback(e){if(!this._isEnabled())return void(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("SDK not enabled, will not capture user feedback."));const t=function(e,{metadata:t,tunnel:n,dsn:r}){const i={event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!r&&{dsn:We(r)}},o=function(e){return[{type:"user_report"},e]}(e);return Qt(i,[o])}(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this._sendEnvelope(t)}_prepareEvent(e,t,n){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,n)}_flushOutcomes(){const e=this._clearOutcomes();if(0===e.length)return void(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log("No outcomes to send"));if(!this._dsn)return void(("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log("No dsn provided, will not send outcomes"));("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log("Sending outcomes:",e);const t=(n=e,Qt((r=this._options.tunnel&&We(this._dsn))?{dsn:r}:{},[[{type:"client_report"},{timestamp:i||Ht(),discarded_events:n}]]));var n,r,i;this._sendEnvelope(t)}}let hr;function pr(e,t=function(){if(hr)return hr;if(lt(zn.fetch))return hr=zn.fetch.bind(zn);const e=zn.document;let t=zn.fetch;if(e&&"function"==typeof e.createElement)try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n);const r=n.contentWindow;r&&r.fetch&&(t=r.fetch),e.head.removeChild(n)}catch(e){("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",e)}return hr=t.bind(zn)}()){let n=0,r=0;return In(e,(function(i){const o=i.body.length;n+=o,r++;const a={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};try{return t(e.url,a).then((e=>(n-=o,r--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}})))}catch(e){return hr=void 0,n-=o,r--,Ut(e)}}))}class fr{static __initStatic(){this.id="GlobalHandlers"}__init(){this.name=fr.id}__init2(){this._installFunc={onerror:vr,onunhandledrejection:gr}}constructor(e){fr.prototype.__init.call(this),fr.prototype.__init2.call(this),this._options={onerror:!0,onunhandledrejection:!0,...e}}setupOnce(){Error.stackTraceLimit=50;const e=this._options;for(const n in e){const r=this._installFunc[n];r&&e[n]&&(t=n,("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.log(`Global Handler attached: ${t}`),r(),this._installFunc[n]=void 0)}var t}}function vr(){yt("error",(e=>{const[t,n,r]=_r();if(!t.getIntegration(fr))return;const{msg:i,url:o,line:a,column:s,error:c}=e;if(Wn()||c&&c.__sentry_own_request__)return;const u=void 0===c&&we(i)?function(e,t,n,r){const i=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;let o=_e(e)?e.message:e,a="Error";const s=o.match(i);s&&(a=s[1],o=s[2]);const c={exception:{values:[{type:a,value:o}]}};return mr(c,t,n,r)}(i,o,a,s):mr(tr(n,c||i,void 0,r,!1),o,a,s);u.level="error",yr(t,c,u,"onerror")}))}function gr(){yt("unhandledrejection",(e=>{const[t,n,r]=_r();if(!t.getIntegration(fr))return;let i=e;try{"reason"in e?i=e.reason:"detail"in e&&"reason"in e.detail&&(i=e.detail.reason)}catch(e){}if(Wn()||i&&i.__sentry_own_request__)return!0;const o=Se(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:tr(n,i,void 0,r,!0);o.level="error",yr(t,i,o,"onunhandledrejection")}))}function mr(e,t,n,r){const i=e.exception=e.exception||{},o=i.values=i.values||[],a=o[0]=o[0]||{},s=a.stacktrace=a.stacktrace||{},c=s.frames=s.frames||[],u=isNaN(parseInt(r,10))?void 0:r,l=isNaN(parseInt(n,10))?void 0:n,d=we(t)&&t.length>0?t:function(){try{return Fe.document.location.href}catch(e){return""}}();return 0===c.length&&c.push({colno:u,filename:d,function:"?",in_app:!0,lineno:l}),e}function yr(e,t,n,r){Dt(n,{handled:!1,type:r}),e.captureEvent(n,{originalException:t})}function _r(){const e=wn(),t=e.getClient(),n=t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return[e,n.stackParser,n.attachStacktrace]}fr.__initStatic();const br=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];class wr{static __initStatic(){this.id="TryCatch"}__init(){this.name=wr.id}constructor(e){wr.prototype.__init.call(this),this._options={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e}}setupOnce(){this._options.setTimeout&&Xe(zn,"setTimeout",Sr),this._options.setInterval&&Xe(zn,"setInterval",Sr),this._options.requestAnimationFrame&&Xe(zn,"requestAnimationFrame",kr),this._options.XMLHttpRequest&&"XMLHttpRequest"in zn&&Xe(XMLHttpRequest.prototype,"send",Er);const e=this._options.eventTarget;if(e){(Array.isArray(e)?e:br).forEach(Mr)}}}function Sr(e){return function(...t){const n=t[0];return t[0]=Hn(n,{mechanism:{data:{function:ct(e)},handled:!0,type:"instrument"}}),e.apply(this,t)}}function kr(e){return function(t){return e.apply(this,[Hn(t,{mechanism:{data:{function:"requestAnimationFrame",handler:ct(e)},handled:!0,type:"instrument"}})])}}function Er(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((e=>{e in n&&"function"==typeof n[e]&&Xe(n,e,(function(t){const n={mechanism:{data:{function:e,handler:ct(t)},handled:!0,type:"instrument"}},r=tt(t);return r&&(n.mechanism.data.handler=ct(r)),Hn(t,n)}))})),e.apply(this,t)}}function Mr(e){const t=zn,n=t[e]&&t[e].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(Xe(n,"addEventListener",(function(t){return function(n,r,i){try{"function"==typeof r.handleEvent&&(r.handleEvent=Hn(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:ct(r),target:e},handled:!0,type:"instrument"}}))}catch(e){}return t.apply(this,[n,Hn(r,{mechanism:{data:{function:"addEventListener",handler:ct(r),target:e},handled:!0,type:"instrument"}}),i])}})),Xe(n,"removeEventListener",(function(e){return function(t,n,r){const i=n;try{const n=i&&i.__sentry_wrapped__;n&&e.call(this,t,n,r)}catch(e){}return e.call(this,t,i,r)}})))}wr.__initStatic();class Cr{static __initStatic(){this.id="LinkedErrors"}__init(){this.name=Cr.id}constructor(e={}){Cr.prototype.__init.call(this),this._key=e.key||"cause",this._limit=e.limit||5}setupOnce(e,t){e(((e,n)=>{const r=t(),i=r.getClient(),o=r.getIntegration(Cr);if(!i||!o)return e;const a=i.getOptions();return xe(Kn,a.stackParser,a.maxValueLength,o._key,o._limit,e,n),e}))}}Cr.__initStatic();class Tr{constructor(){Tr.prototype.__init.call(this)}static __initStatic(){this.id="HttpContext"}__init(){this.name=Tr.id}setupOnce(){fn((e=>{if(wn().getIntegration(Tr)){if(!zn.navigator&&!zn.location&&!zn.document)return e;const t=e.request&&e.request.url||zn.location&&zn.location.href,{referrer:n}=zn.document||{},{userAgent:r}=zn.navigator||{},i={...e.request&&e.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},o={...e.request,...t&&{url:t},headers:i};return{...e,request:o}}return e}))}}Tr.__initStatic();class Or{constructor(){Or.prototype.__init.call(this)}static __initStatic(){this.id="Dedupe"}__init(){this.name=Or.id}setupOnce(e,t){const n=e=>{if(e.type)return e;const n=t().getIntegration(Or);if(n){try{if(function(e,t){if(!t)return!1;if(function(e,t){const n=e.message,r=t.message;if(!n&&!r)return!1;if(n&&!r||!n&&r)return!1;if(n!==r)return!1;if(!Pr(e,t))return!1;if(!jr(e,t))return!1;return!0}(e,t))return!0;if(function(e,t){const n=xr(t),r=xr(e);if(!n||!r)return!1;if(n.type!==r.type||n.value!==r.value)return!1;if(!Pr(e,t))return!1;if(!jr(e,t))return!1;return!0}(e,t))return!0;return!1}(e,n._previousEvent))return("undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__)&&ze.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(t){return n._previousEvent=e}return n._previousEvent=e}return e};n.id=this.name,e(n)}}function jr(e,t){let n=Ar(e),r=Ar(t);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(r.length!==n.length)return!1;for(let e=0;e<r.length;e++){const t=r[e],i=n[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function Pr(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(e){return!1}}function xr(e){return e.exception&&e.exception.values&&e.exception.values[0]}function Ar(e){const t=e.exception;if(t)try{return t.values[0].stacktrace.frames}catch(e){return}}Or.__initStatic();var Dr=Object.freeze({__proto__:null,Breadcrumbs:ar,Dedupe:Or,GlobalHandlers:fr,HttpContext:Tr,LinkedErrors:Cr,TryCatch:wr});let Lr={};zn.Sentry&&zn.Sentry.Integrations&&(Lr=zn.Sentry.Integrations);const Nr={...Lr,...Yn,...Dr};var Ir="new",Rr="loading",Br="loaded",Fr="joining-meeting",Ur="joined-meeting",Vr="left-meeting",$r="error",Gr="blocked",qr="off",Yr="sendable",zr="loading",Jr="interrupted",Wr="playable",Hr="unknown",Kr="full",Qr="lobby",Xr="none",Zr="base",ei="*",ti="ejected",ni="nbf-room",ri="nbf-token",ii="exp-room",oi="exp-token",ai="no-room",si="meeting-full",ci="end-of-life",ui="not-allowed",li="connection-error",di="cam-in-use",hi="mic-in-use",pi="cam-mic-in-use",fi="permissions",vi="undefined-mediadevices",gi="not-found",mi="constraints",yi="unknown",_i="iframe-ready-for-launch-config",bi="iframe-launch-config",wi="theme-updated",Si="loading",ki="load-attempt-failed",Ei="loaded",Mi="started-camera",Ci="camera-error",Ti="joining-meeting",Oi="joined-meeting",ji="left-meeting",Pi="available-devices-updated",xi="participant-joined",Ai="participant-updated",Di="participant-left",Li="participant-counts-updated",Ni="access-state-updated",Ii="meeting-session-summary-updated",Ri="meeting-session-state-updated",Bi="meeting-session-data-error",Fi="waiting-participant-added",Ui="waiting-participant-updated",Vi="waiting-participant-removed",$i="track-started",Gi="track-stopped",qi="transcription-started",Yi="transcription-stopped",zi="transcription-error",Ji="recording-started",Wi="recording-stopped",Hi="recording-stats",Ki="recording-error",Qi="recording-upload-completed",Xi="recording-data",Zi="app-message",eo="remote-media-player-started",to="remote-media-player-updated",no="remote-media-player-stopped",ro="local-screen-share-started",io="local-screen-share-stopped",oo="active-speaker-change",ao="active-speaker-mode-change",so="network-quality-change",co="network-connection",uo="cpu-load-change",lo="fullscreen",ho="exited-fullscreen",po="live-streaming-started",fo="live-streaming-updated",vo="live-streaming-stopped",go="live-streaming-error",mo="lang-updated",yo="receive-settings-updated",_o="input-settings-updated",bo="nonfatal-error",wo="error",So=102400,ko="iframe-call-message",Eo="local-screen-start",Mo="register-input-handler",Co="daily-method-update-live-streaming-endpoints",To="transmit-log",Oo="daily-custom-track",jo={NONE:"none",BGBLUR:"background-blur",BGIMAGE:"background-image"},Po={NONE:"none",NOISE_CANCELLATION:"noise-cancellation"},xo={PLAY:"play",PAUSE:"pause"},Ao=10,Do=["jpg","png","jpeg"],Lo="add-endpoints",No="remove-endpoints";function Io(){return!Ro()&&"undefined"!=typeof window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:""}function Ro(){return"undefined"!=typeof navigator&&navigator.product&&"ReactNative"===navigator.product}function Bo(){return navigator&&navigator.mediaDevices&&navigator.mediaDevices.getUserMedia}function Fo(){return!!(navigator&&navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia)&&(function(e,t){if(!e||!t)return!0;switch(e){case"Chrome":return t.major>=75;case"Safari":return RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection")&&!(13===t.major&&0===t.minor&&0===t.point);case"Firefox":return t.major>=67}return!0}(Jo(),function(){switch(Jo()){case"Chrome":return Wo();case"Safari":return Ko();case"Firefox":return Qo();case"Edge":return function(){var e=0,t=0;if("undefined"!=typeof window){var n=Io().match(/Edge\/(\d+).(\d+)/);if(n)try{e=parseInt(n[1]),t=parseInt(n[2])}catch(e){}}return{major:e,minor:t}}()}}())||Ro())}function Uo(){if(Ro())return!1;if(!document)return!1;var e=document.createElement("iframe");return!!e.requestFullscreen||!!e.webkitRequestFullscreen}var Vo=["Chrome","Firefox"];function $o(){return!Ro()&&(!zo()&&Vo.includes(Jo()))}var Go=["Chrome","Firefox"];function qo(){return!Ro()&&(!zo()&&("undefined"!=typeof AudioWorkletNode&&Go.includes(Jo())))}function Yo(){return Bo()&&!function(){var e,t=Jo();if(!Io())return!0;switch(t){case"Chrome":return(e=Wo()).major&&e.major>0&&e.major<61;case"Firefox":return(e=Qo()).major<78;case"Safari":return(e=Ko()).major<12;default:return!0}}()}function zo(){var e,t,n=Io(),r=n.match(/Mac/)&&(!Ro()&&"undefined"!=typeof window&&null!==(e=window)&&void 0!==e&&null!==(t=e.navigator)&&void 0!==t&&t.maxTouchPoints?window.navigator.maxTouchPoints:0)>=5;return!!(n.match(/Mobi/)||n.match(/Android/)||r)||(!!Io().match(/DailyAnd\//)||void 0)}function Jo(){if("undefined"!=typeof window){var e=Io();return Ho()?"Safari":e.indexOf("Edge")>-1?"Edge":e.match(/Chrome\//)?"Chrome":e.indexOf("Safari")>-1?"Safari":e.indexOf("Firefox")>-1?"Firefox":e.indexOf("MSIE")>-1||e.indexOf(".NET")>-1?"IE":"Unknown Browser"}}function Wo(){var e=0,t=0,n=0,r=0,i=!1;if("undefined"!=typeof window){var o=Io(),a=o.match(/Chrome\/(\d+).(\d+).(\d+).(\d+)/);if(a)try{e=parseInt(a[1]),t=parseInt(a[2]),n=parseInt(a[3]),r=parseInt(a[4]),i=o.indexOf("OPR/")>-1}catch(e){}}return{major:e,minor:t,build:n,patch:r,opera:i}}function Ho(){return!!Io().match(/iPad|iPhone|iPod/i)&&Bo()}function Ko(){var e=0,t=0,n=0;if("undefined"!=typeof window){var r=Io().match(/Version\/(\d+).(\d+)(.(\d+))?/);if(r)try{e=parseInt(r[1]),t=parseInt(r[2]),n=parseInt(r[4])}catch(e){}else Ho()&&(e=14,t=0,n=3)}return{major:e,minor:t,point:n}}function Qo(){var e=0,t=0;if("undefined"!=typeof window){var n=Io().match(/Firefox\/(\d+).(\d+)/);if(n)try{e=parseInt(n[1]),t=parseInt(n[2])}catch(e){}}return{major:e,minor:t}}var Xo=function(){function e(){r(this,e)}return s(e,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,n){he()}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,n){he()}},{key:"sendMessageToCallMachine",value:function(e,t,n,r){he()}},{key:"sendMessageToDailyJs",value:function(e,t){he()}},{key:"removeListener",value:function(e){he()}}]),e}();function Zo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ea(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Zo(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ta(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=h(e);if(t){var i=h(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d(this,n)}}var na=function(e){l(n,Xo);var t=ta(n);function n(){var e;return r(this,n),(e=t.call(this))._wrappedListeners={},e._messageCallbacks={},e}return s(n,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,n){var r=this,i=function(i){if(i.data&&"iframe-call-message"===i.data.what&&(!i.data.callFrameId||i.data.callFrameId===t)&&(!i.data.from||"module"!==i.data.from)){var o=ea({},i.data);if(delete o.from,o.callbackStamp&&r._messageCallbacks[o.callbackStamp]){var a=o.callbackStamp;r._messageCallbacks[a].call(n,o),delete r._messageCallbacks[a]}delete o.what,delete o.callbackStamp,e.call(n,o)}};this._wrappedListeners[e]=i,window.addEventListener("message",i)}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,n){var r=function(r){if(!(!r.data||r.data.what!==ko||!r.data.action||r.data.from&&"module"!==r.data.from||r.data.callFrameId&&t&&r.data.callFrameId!==t)){var i=r.data;e.call(n,i)}};this._wrappedListeners[e]=r,window.addEventListener("message",r)}},{key:"sendMessageToCallMachine",value:function(e,t,n,r){if(!r)throw new Error("undefined callFrameId. Are you trying to use a DailyCall instance previously destroyed?");var i=ea({},e);if(i.what=ko,i.from="module",i.callFrameId=r,t){var o=de();this._messageCallbacks[o]=t,i.callbackStamp=o}(n?n.contentWindow:window).postMessage(i,"*")}},{key:"sendMessageToDailyJs",value:function(e,t){e.what=ko,e.callFrameId=t,e.from="embedded",window.postMessage(e,"*")}},{key:"removeListener",value:function(e){var t=this._wrappedListeners[e];t&&(window.removeEventListener("message",t),delete this._wrappedListeners[e])}},{key:"forwardPackagedMessageToCallMachine",value:function(e,t,n){var r=ea({},e);r.callFrameId=n,(t?t.contentWindow:window).postMessage(r,"*")}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e,t){var n=function(n){if(n.data&&"iframe-call-message"===n.data.what&&(!n.data.callFrameId||n.data.callFrameId===t)&&(!n.data.from||"module"!==n.data.from)){var r=n.data;e(r)}};return this._wrappedListeners[e]=n,window.addEventListener("message",n),e}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){var t=this._wrappedListeners[e];t&&(window.removeEventListener("message",t),delete this._wrappedListeners[e])}}]),n}();function ra(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=h(e);if(t){var i=h(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d(this,n)}}var ia=function(e){l(n,Xo);var t=ra(n);function n(){var e;return r(this,n),e=t.call(this),global.callMachineToDailyJsEmitter=global.callMachineToDailyJsEmitter||new y.EventEmitter,global.dailyJsToCallMachineEmitter=global.dailyJsToCallMachineEmitter||new y.EventEmitter,e._wrappedListeners={},e._messageCallbacks={},e}return s(n,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,n){this._addListener(e,global.callMachineToDailyJsEmitter,n,"received call machine message")}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,n){this._addListener(e,global.dailyJsToCallMachineEmitter,n,"received daily-js message")}},{key:"sendMessageToCallMachine",value:function(e,t){this._sendMessage(e,global.dailyJsToCallMachineEmitter,"sending message to call machine",t)}},{key:"sendMessageToDailyJs",value:function(e){this._sendMessage(e,global.callMachineToDailyJsEmitter,"sending message to daily-js")}},{key:"removeListener",value:function(e){var t=this._wrappedListeners[e];t&&(global.callMachineToDailyJsEmitter.removeListener("message",t),global.dailyJsToCallMachineEmitter.removeListener("message",t),delete this._wrappedListeners[e])}},{key:"_addListener",value:function(e,t,n,r){var i=this,o=function(t){if(t.callbackStamp&&i._messageCallbacks[t.callbackStamp]){var r=t.callbackStamp;i._messageCallbacks[r].call(n,t),delete i._messageCallbacks[r]}e.call(n,t)};this._wrappedListeners[e]=o,t.addListener("message",o)}},{key:"_sendMessage",value:function(e,t,n,r){if(r){var i=de();this._messageCallbacks[i]=r,e.callbackStamp=i}t.emit("message",e)}}]),n}(),oa=Object.prototype.hasOwnProperty;function aa(e,t,n){for(n of e.keys())if(sa(n,t))return n}function sa(e,t){var n,r,i;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&sa(e[r],t[r]););return-1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e){if((i=r)&&"object"==typeof i&&!(i=aa(t,i)))return!1;if(!t.has(i))return!1}return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e){if((i=r[0])&&"object"==typeof i&&!(i=aa(t,i)))return!1;if(!sa(r[1],t.get(i)))return!1}return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return-1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return-1===r}if(!n||"object"==typeof e){for(n in r=0,e){if(oa.call(e,n)&&++r&&!oa.call(t,n))return!1;if(!(n in t)||!sa(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!=e&&t!=t}var ca="replace",ua="shallow-merge",la=[ca,ua];var da=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.data,i=t.mergeStrategy,o=void 0===i?ca:i;r(this,e),e._validateMergeStrategy(o),e._validateData(n,o),this.mergeStrategy=o,this.data=n}return s(e,[{key:"isNoOp",value:function(){return e.isNoOpUpdate(this.data,this.mergeStrategy)}}],[{key:"isNoOpUpdate",value:function(e,t){return 0===Object.keys(e).length&&t===ua}},{key:"_validateMergeStrategy",value:function(e){if(!la.includes(e))throw Error("Unrecognized mergeStrategy provided. Options are: [".concat(la,"]"))}},{key:"_validateData",value:function(e,t){if(!function(e){if(null==e||"object"!==i(e))return!1;var t=Object.getPrototypeOf(e);return null==t||t===Object.prototype}(e))throw Error("Meeting session data must be a plain (map-like) object");var n;try{if(n=JSON.stringify(e),t===ca){var r=JSON.parse(n);sa(r,e)||console.warn("The meeting session data provided will be modified when serialized.",r,e)}else if(t===ua)for(var o in e)if(Object.hasOwnProperty.call(e,o)&&void 0!==e[o]){var a=JSON.parse(JSON.stringify(e[o]));sa(e[o],a)||console.warn("At least one key in the meeting session data provided will be modified when serialized.",a,e[o])}}catch(e){throw Error("Meeting session data must be serializable to JSON: ".concat(e))}if(n.length>So)throw Error("Meeting session data is too large (".concat(n.length," characters). Maximum size suppported is ").concat(So,"."))}}]),e}();function ha(e,t,n){return ha=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct.bind():function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&u(i,n.prototype),i},ha.apply(null,arguments)}function pa(e){var t="function"==typeof Map?new Map:void 0;return pa=function(e){if(null===e||(n=e,-1===Function.toString.call(n).indexOf("[native code]")))return e;var n;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return ha(e,arguments,h(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),u(r,e)},pa(e)}function fa(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=h(e);if(t){var i=h(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d(this,n)}}var va=function(){function e(){r(this,e),this._currentLoad=null}return s(e,[{key:"load",value:function(e,t,n,r){if(this.loaded)return window._dailyCallObjectSetup(e),void n(!0);!function(e,t){window._dailyConfig||(window._dailyConfig={}),window._dailyConfig.callFrameId=e,window._dailyConfig.avoidEval=t}(e,t),this._currentLoad&&this._currentLoad.cancel(),this._currentLoad=new ga((function(){n(!1)}),r),this._currentLoad.start()}},{key:"cancel",value:function(){this._currentLoad&&this._currentLoad.cancel()}},{key:"loaded",get:function(){return this._currentLoad&&this._currentLoad.succeeded}}]),e}(),ga=function(){function e(t,n){r(this,e),this._attemptsRemaining=3,this._currentAttempt=null,this._successCallback=t,this._failureCallback=n}return s(e,[{key:"start",value:function(){var e=this;if(!this._currentAttempt){this._currentAttempt=new _a(this._successCallback,(function t(n){e._currentAttempt.cancelled||(e._attemptsRemaining--,e._failureCallback(n,e._attemptsRemaining>0),e._attemptsRemaining<=0||setTimeout((function(){e._currentAttempt.cancelled||(e._currentAttempt=new _a(e._successCallback,t),e._currentAttempt.start())}),3e3))})),this._currentAttempt.start()}}},{key:"cancel",value:function(){this._currentAttempt&&this._currentAttempt.cancel()}},{key:"cancelled",get:function(){return this._currentAttempt&&this._currentAttempt.cancelled}},{key:"succeeded",get:function(){return this._currentAttempt&&this._currentAttempt.succeeded}}]),e}(),ma=function(e){l(n,pa(Error));var t=fa(n);function n(){return r(this,n),t.apply(this,arguments)}return s(n)}(),ya=2e4,_a=function(){function e(t,n){r(this,e),this._loadAttemptImpl=Ro()||!_dailyConfig.avoidEval?new ba(t,n):new wa(t,n)}var t;return s(e,[{key:"start",value:(t=n((function*(){return this._loadAttemptImpl.start()})),function(){return t.apply(this,arguments)})},{key:"cancel",value:function(){this._loadAttemptImpl.cancel()}},{key:"cancelled",get:function(){return this._loadAttemptImpl.cancelled}},{key:"succeeded",get:function(){return this._loadAttemptImpl.succeeded}}]),e}(),ba=function(){function e(t,n){r(this,e),this.cancelled=!1,this.succeeded=!1,this._networkTimedOut=!1,this._networkTimeout=null,this._iosCache="undefined"!=typeof iOSCallObjectBundleCache&&iOSCallObjectBundleCache,this._refetchHeaders=null,this._successCallback=t,this._failureCallback=n}var t,i,o,a;return s(e,[{key:"start",value:(a=n((function*(){var e=fe();!(yield this._tryLoadFromIOSCache(e))&&this._loadFromNetwork(e)})),function(){return a.apply(this,arguments)})},{key:"cancel",value:function(){clearTimeout(this._networkTimeout),this.cancelled=!0}},{key:"_tryLoadFromIOSCache",value:(o=n((function*(e){if(!this._iosCache)return!1;try{var t=yield this._iosCache.get(e);return!!this.cancelled||!!t&&(t.code?(Function('"use strict";'+t.code)(),this.succeeded=!0,this._successCallback(),!0):(this._refetchHeaders=t.refetchHeaders,!1))}catch(e){return!1}})),function(e){return o.apply(this,arguments)})},{key:"_loadFromNetwork",value:(i=n((function*(e){var t=this;this._networkTimeout=setTimeout((function(){t._networkTimedOut=!0,t._failureCallback("Timed out (>".concat(ya," ms) when loading call object bundle ").concat(e))}),ya);try{var n=this._refetchHeaders?{headers:this._refetchHeaders}:{},r=yield fetch(e,n);if(clearTimeout(this._networkTimeout),this.cancelled||this._networkTimedOut)throw new ma;var i=yield this._getBundleCodeFromResponse(e,r);if(this.cancelled)throw new ma;Function('"use strict";'+i)(),this._iosCache&&this._iosCache.set(e,i,r.headers),this.succeeded=!0,this._successCallback()}catch(t){if(clearTimeout(this._networkTimeout),t instanceof ma||this.cancelled||this._networkTimedOut)return;this._failureCallback("Failed to load call object bundle ".concat(e,": ").concat(t))}})),function(e){return i.apply(this,arguments)})},{key:"_getBundleCodeFromResponse",value:(t=n((function*(e,t){if(t.ok)return yield t.text();if(this._iosCache&&304===t.status)return(yield this._iosCache.renew(e,t.headers)).code;throw new Error("Received ".concat(t.status," response"))})),function(e,n){return t.apply(this,arguments)})}]),e}(),wa=function(){function e(t,n){r(this,e),this.cancelled=!1,this.succeeded=!1,this._successCallback=t,this._failureCallback=n,this._attemptId=de(),this._networkTimeout=null,this._scriptElement=null}var t;return s(e,[{key:"start",value:(t=n((function*(){window._dailyCallMachineLoadWaitlist||(window._dailyCallMachineLoadWaitlist=new Set);var e=fe();"object"===("undefined"==typeof document?"undefined":i(document))?this._startLoading(e):this._failureCallback("Call object bundle must be loaded in a DOM/web context")})),function(){return t.apply(this,arguments)})},{key:"cancel",value:function(){this._stopLoading(),this.cancelled=!0}},{key:"_startLoading",value:function(e){var t=this;this._signUpForCallMachineLoadWaitlist(),this._networkTimeout=setTimeout((function(){t._stopLoading(),t._failureCallback("Timed out (>".concat(ya," ms) when loading call object bundle ").concat(e))}),ya);var r=document.getElementsByTagName("head")[0],i=document.createElement("script");this._scriptElement=i,i.onload=n((function*(){t._stopLoading(),t.succeeded=!0,t._successCallback()})),i.onerror=function(){var e=n((function*(e){t._stopLoading(),t._failureCallback("Failed to load call object bundle ".concat(e.target.src))}));return function(t){return e.apply(this,arguments)}}(),i.src=e,r.appendChild(i)}},{key:"_stopLoading",value:function(){this._withdrawFromCallMachineLoadWaitlist(),clearTimeout(this._networkTimeout),this._scriptElement&&(this._scriptElement.onload=null,this._scriptElement.onerror=null)}},{key:"_signUpForCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.add(this._attemptId)}},{key:"_withdrawFromCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.delete(this._attemptId)}}]),e}();var Sa=function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,o=[];++n<r;){var a=e[n];t(a,n,e)&&(o[i++]=a)}return o};var ka=function(e){return function(t,n,r){for(var i=-1,o=Object(t),a=r(t),s=a.length;s--;){var c=a[e?s:++i];if(!1===n(o[c],c,o))break}return t}},Ea=ka();var Ma=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},Ca="object"==typeof m&&m&&m.Object===Object&&m,Ta=Ca,Oa="object"==typeof self&&self&&self.Object===Object&&self,ja=Ta||Oa||Function("return this")(),Pa=ja.Symbol,xa=Pa,Aa=Object.prototype,Da=Aa.hasOwnProperty,La=Aa.toString,Na=xa?xa.toStringTag:void 0;var Ia=function(e){var t=Da.call(e,Na),n=e[Na];try{e[Na]=void 0;var r=!0}catch(e){}var i=La.call(e);return r&&(t?e[Na]=n:delete e[Na]),i},Ra=Object.prototype.toString;var Ba=Ia,Fa=function(e){return Ra.call(e)},Ua=Pa?Pa.toStringTag:void 0;var Va=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Ua&&Ua in Object(e)?Ba(e):Fa(e)};var $a=function(e){return null!=e&&"object"==typeof e},Ga=Va,qa=$a;var Ya=function(e){return qa(e)&&"[object Arguments]"==Ga(e)},za=$a,Ja=Object.prototype,Wa=Ja.hasOwnProperty,Ha=Ja.propertyIsEnumerable,Ka=Ya(function(){return arguments}())?Ya:function(e){return za(e)&&Wa.call(e,"callee")&&!Ha.call(e,"callee")},Qa=Array.isArray,Xa={};var Za=function(){return!1};!function(e,t){var n=ja,r=Za,i=t&&!t.nodeType&&t,o=i&&e&&!e.nodeType&&e,a=o&&o.exports===i?n.Buffer:void 0,s=(a?a.isBuffer:void 0)||r;e.exports=s}({get exports(){return Xa},set exports(e){Xa=e}},Xa);var es=/^(?:0|[1-9]\d*)$/;var ts=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&es.test(e))&&e>-1&&e%1==0&&e<t};var ns=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},rs=Va,is=ns,os=$a,as={};as["[object Float32Array]"]=as["[object Float64Array]"]=as["[object Int8Array]"]=as["[object Int16Array]"]=as["[object Int32Array]"]=as["[object Uint8Array]"]=as["[object Uint8ClampedArray]"]=as["[object Uint16Array]"]=as["[object Uint32Array]"]=!0,as["[object Arguments]"]=as["[object Array]"]=as["[object ArrayBuffer]"]=as["[object Boolean]"]=as["[object DataView]"]=as["[object Date]"]=as["[object Error]"]=as["[object Function]"]=as["[object Map]"]=as["[object Number]"]=as["[object Object]"]=as["[object RegExp]"]=as["[object Set]"]=as["[object String]"]=as["[object WeakMap]"]=!1;var ss=function(e){return os(e)&&is(e.length)&&!!as[rs(e)]};var cs=function(e){return function(t){return e(t)}},us={};!function(e,t){var n=Ca,r=t&&!t.nodeType&&t,i=r&&e&&!e.nodeType&&e,o=i&&i.exports===r&&n.process,a=function(){try{var e=i&&i.require&&i.require("util").types;return e||o&&o.binding&&o.binding("util")}catch(e){}}();e.exports=a}({get exports(){return us},set exports(e){us=e}},us);var ls=ss,ds=cs,hs=us&&us.isTypedArray,ps=hs?ds(hs):ls,fs=Ma,vs=Ka,gs=Qa,ms=Xa,ys=ts,_s=ps,bs=Object.prototype.hasOwnProperty;var ws=function(e,t){var n=gs(e),r=!n&&vs(e),i=!n&&!r&&ms(e),o=!n&&!r&&!i&&_s(e),a=n||r||i||o,s=a?fs(e.length,String):[],c=s.length;for(var u in e)!t&&!bs.call(e,u)||a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||ys(u,c))||s.push(u);return s},Ss=Object.prototype;var ks=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ss)};var Es=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object),Ms=ks,Cs=Es,Ts=Object.prototype.hasOwnProperty;var Os=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},js=Va,Ps=Os;var xs=function(e){if(!Ps(e))return!1;var t=js(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},As=xs,Ds=ns;var Ls=function(e){return null!=e&&Ds(e.length)&&!As(e)},Ns=ws,Is=function(e){if(!Ms(e))return Cs(e);var t=[];for(var n in Object(e))Ts.call(e,n)&&"constructor"!=n&&t.push(n);return t},Rs=Ls;var Bs=function(e){return Rs(e)?Ns(e):Is(e)},Fs=Ea,Us=Bs;var Vs=Ls;var $s=function(e,t){return function(n,r){if(null==n)return n;if(!Vs(n))return e(n,r);for(var i=n.length,o=t?i:-1,a=Object(n);(t?o--:++o<i)&&!1!==r(a[o],o,a););return n}},Gs=$s((function(e,t){return e&&Fs(e,t,Us)})),qs=Gs;var Ys=function(e,t){var n=[];return qs(e,(function(e,r,i){t(e,r,i)&&n.push(e)})),n};var zs=function(){this.__data__=[],this.size=0};var Js=function(e,t){return e===t||e!=e&&t!=t},Ws=Js;var Hs=function(e,t){for(var n=e.length;n--;)if(Ws(e[n][0],t))return n;return-1},Ks=Hs,Qs=Array.prototype.splice;var Xs=function(e){var t=this.__data__,n=Ks(t,e);return!(n<0)&&(n==t.length-1?t.pop():Qs.call(t,n,1),--this.size,!0)},Zs=Hs;var ec=function(e){var t=this.__data__,n=Zs(t,e);return n<0?void 0:t[n][1]},tc=Hs;var nc=Hs;var rc=function(e,t){var n=this.__data__,r=nc(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},ic=zs,oc=Xs,ac=ec,sc=function(e){return tc(this.__data__,e)>-1},cc=rc;function uc(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}uc.prototype.clear=ic,uc.prototype.delete=oc,uc.prototype.get=ac,uc.prototype.has=sc,uc.prototype.set=cc;var lc=uc,dc=lc;var hc=function(){this.__data__=new dc,this.size=0};var pc=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};var fc=function(e){return this.__data__.get(e)};var vc,gc=function(e){return this.__data__.has(e)},mc=ja["__core-js_shared__"],yc=(vc=/[^.]+$/.exec(mc&&mc.keys&&mc.keys.IE_PROTO||""))?"Symbol(src)_1."+vc:"";var _c=function(e){return!!yc&&yc in e},bc=Function.prototype.toString;var wc=function(e){if(null!=e){try{return bc.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Sc=xs,kc=_c,Ec=Os,Mc=wc,Cc=/^\[object .+?Constructor\]$/,Tc=Function.prototype,Oc=Object.prototype,jc=Tc.toString,Pc=Oc.hasOwnProperty,xc=RegExp("^"+jc.call(Pc).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var Ac=function(e){return!(!Ec(e)||kc(e))&&(Sc(e)?xc:Cc).test(Mc(e))},Dc=function(e,t){return null==e?void 0:e[t]};var Lc=function(e,t){var n=Dc(e,t);return Ac(n)?n:void 0},Nc=Lc(ja,"Map"),Ic=Lc(Object,"create"),Rc=Ic;var Bc=function(){this.__data__=Rc?Rc(null):{},this.size=0};var Fc=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Uc=Ic,Vc=Object.prototype.hasOwnProperty;var $c=function(e){var t=this.__data__;if(Uc){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Vc.call(t,e)?t[e]:void 0},Gc=Ic,qc=Object.prototype.hasOwnProperty;var Yc=Ic;var zc=Bc,Jc=Fc,Wc=$c,Hc=function(e){var t=this.__data__;return Gc?void 0!==t[e]:qc.call(t,e)},Kc=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Yc&&void 0===t?"__lodash_hash_undefined__":t,this};function Qc(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Qc.prototype.clear=zc,Qc.prototype.delete=Jc,Qc.prototype.get=Wc,Qc.prototype.has=Hc,Qc.prototype.set=Kc;var Xc=Qc,Zc=lc,eu=Nc;var tu=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var nu=function(e,t){var n=e.__data__;return tu(t)?n["string"==typeof t?"string":"hash"]:n.map},ru=nu;var iu=nu;var ou=nu;var au=nu;var su=function(){this.size=0,this.__data__={hash:new Xc,map:new(eu||Zc),string:new Xc}},cu=function(e){var t=ru(this,e).delete(e);return this.size-=t?1:0,t},uu=function(e){return iu(this,e).get(e)},lu=function(e){return ou(this,e).has(e)},du=function(e,t){var n=au(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function hu(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}hu.prototype.clear=su,hu.prototype.delete=cu,hu.prototype.get=uu,hu.prototype.has=lu,hu.prototype.set=du;var pu=hu,fu=lc,vu=Nc,gu=pu;var mu=lc,yu=hc,_u=pc,bu=fc,wu=gc,Su=function(e,t){var n=this.__data__;if(n instanceof fu){var r=n.__data__;if(!vu||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new gu(r)}return n.set(e,t),this.size=n.size,this};function ku(e){var t=this.__data__=new mu(e);this.size=t.size}ku.prototype.clear=yu,ku.prototype.delete=_u,ku.prototype.get=bu,ku.prototype.has=wu,ku.prototype.set=Su;var Eu=ku;var Mu=pu,Cu=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},Tu=function(e){return this.__data__.has(e)};function Ou(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Mu;++t<n;)this.add(e[t])}Ou.prototype.add=Ou.prototype.push=Cu,Ou.prototype.has=Tu;var ju=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1};var Pu=Ou,xu=ju,Au=function(e,t){return e.has(t)};var Du=function(e,t,n,r,i,o){var a=1&n,s=e.length,c=t.length;if(s!=c&&!(a&&c>s))return!1;var u=o.get(e),l=o.get(t);if(u&&l)return u==t&&l==e;var d=-1,h=!0,p=2&n?new Pu:void 0;for(o.set(e,t),o.set(t,e);++d<s;){var f=e[d],v=t[d];if(r)var g=a?r(v,f,d,t,e,o):r(f,v,d,e,t,o);if(void 0!==g){if(g)continue;h=!1;break}if(p){if(!xu(t,(function(e,t){if(!Au(p,t)&&(f===e||i(f,e,n,r,o)))return p.push(t)}))){h=!1;break}}else if(f!==v&&!i(f,v,n,r,o)){h=!1;break}}return o.delete(e),o.delete(t),h};var Lu=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n};var Nu=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n},Iu=ja.Uint8Array,Ru=Js,Bu=Du,Fu=Lu,Uu=Nu,Vu=Pa?Pa.prototype:void 0,$u=Vu?Vu.valueOf:void 0;var Gu=function(e,t,n,r,i,o,a){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!o(new Iu(e),new Iu(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Ru(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=Fu;case"[object Set]":var c=1&r;if(s||(s=Uu),e.size!=t.size&&!c)return!1;var u=a.get(e);if(u)return u==t;r|=2,a.set(e,t);var l=Bu(s(e),s(t),r,i,o,a);return a.delete(e),l;case"[object Symbol]":if($u)return $u.call(e)==$u.call(t)}return!1};var qu=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e},Yu=qu,zu=Qa;var Ju=function(e,t,n){var r=t(e);return zu(e)?r:Yu(r,n(e))};var Wu=Sa,Hu=function(){return[]},Ku=Object.prototype.propertyIsEnumerable,Qu=Object.getOwnPropertySymbols,Xu=Ju,Zu=Qu?function(e){return null==e?[]:(e=Object(e),Wu(Qu(e),(function(t){return Ku.call(e,t)})))}:Hu,el=Bs;var tl=function(e){return Xu(e,el,Zu)},nl=Object.prototype.hasOwnProperty;var rl=function(e,t,n,r,i,o){var a=1&n,s=tl(e),c=s.length;if(c!=tl(t).length&&!a)return!1;for(var u=c;u--;){var l=s[u];if(!(a?l in t:nl.call(t,l)))return!1}var d=o.get(e),h=o.get(t);if(d&&h)return d==t&&h==e;var p=!0;o.set(e,t),o.set(t,e);for(var f=a;++u<c;){var v=e[l=s[u]],g=t[l];if(r)var m=a?r(g,v,l,t,e,o):r(v,g,l,e,t,o);if(!(void 0===m?v===g||i(v,g,n,r,o):m)){p=!1;break}f||(f="constructor"==l)}if(p&&!f){var y=e.constructor,_=t.constructor;y==_||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof _&&_ instanceof _||(p=!1)}return o.delete(e),o.delete(t),p},il=Lc(ja,"DataView"),ol=Nc,al=Lc(ja,"Promise"),sl=Lc(ja,"Set"),cl=Lc(ja,"WeakMap"),ul=Va,ll=wc,dl="[object Map]",hl="[object Promise]",pl="[object Set]",fl="[object WeakMap]",vl="[object DataView]",gl=ll(il),ml=ll(ol),yl=ll(al),_l=ll(sl),bl=ll(cl),wl=ul;(il&&wl(new il(new ArrayBuffer(1)))!=vl||ol&&wl(new ol)!=dl||al&&wl(al.resolve())!=hl||sl&&wl(new sl)!=pl||cl&&wl(new cl)!=fl)&&(wl=function(e){var t=ul(e),n="[object Object]"==t?e.constructor:void 0,r=n?ll(n):"";if(r)switch(r){case gl:return vl;case ml:return dl;case yl:return hl;case _l:return pl;case bl:return fl}return t});var Sl=Eu,kl=Du,El=Gu,Ml=rl,Cl=wl,Tl=Qa,Ol=Xa,jl=ps,Pl="[object Arguments]",xl="[object Array]",Al="[object Object]",Dl=Object.prototype.hasOwnProperty;var Ll=function(e,t,n,r,i,o){var a=Tl(e),s=Tl(t),c=a?xl:Cl(e),u=s?xl:Cl(t),l=(c=c==Pl?Al:c)==Al,d=(u=u==Pl?Al:u)==Al,h=c==u;if(h&&Ol(e)){if(!Ol(t))return!1;a=!0,l=!1}if(h&&!l)return o||(o=new Sl),a||jl(e)?kl(e,t,n,r,i,o):El(e,t,c,n,r,i,o);if(!(1&n)){var p=l&&Dl.call(e,"__wrapped__"),f=d&&Dl.call(t,"__wrapped__");if(p||f){var v=p?e.value():e,g=f?t.value():t;return o||(o=new Sl),i(v,g,n,r,o)}}return!!h&&(o||(o=new Sl),Ml(e,t,n,r,i,o))},Nl=$a;var Il=function e(t,n,r,i,o){return t===n||(null==t||null==n||!Nl(t)&&!Nl(n)?t!=t&&n!=n:Ll(t,n,r,i,e,o))},Rl=Eu,Bl=Il;var Fl=function(e,t,n,r){var i=n.length,o=i,a=!r;if(null==e)return!o;for(e=Object(e);i--;){var s=n[i];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<o;){var c=(s=n[i])[0],u=e[c],l=s[1];if(a&&s[2]){if(void 0===u&&!(c in e))return!1}else{var d=new Rl;if(r)var h=r(u,l,c,e,t,d);if(!(void 0===h?Bl(l,u,3,r,d):h))return!1}}return!0},Ul=Os;var Vl=function(e){return e==e&&!Ul(e)},$l=Vl,Gl=Bs;var ql=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}},Yl=Fl,zl=function(e){for(var t=Gl(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,$l(i)]}return t},Jl=ql;var Wl=function(e){var t=zl(e);return 1==t.length&&t[0][2]?Jl(t[0][0],t[0][1]):function(n){return n===e||Yl(n,e,t)}},Hl=Va,Kl=$a;var Ql=function(e){return"symbol"==typeof e||Kl(e)&&"[object Symbol]"==Hl(e)},Xl=Qa,Zl=Ql,ed=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,td=/^\w*$/;var nd=function(e,t){if(Xl(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Zl(e))||(td.test(e)||!ed.test(e)||null!=t&&e in Object(t))},rd=pu;function id(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var a=e.apply(this,r);return n.cache=o.set(i,a)||o,a};return n.cache=new(id.Cache||rd),n}id.Cache=rd;var od=id;var ad=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,sd=/\\(\\)?/g,cd=function(e){var t=od(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(ad,(function(e,n,r,i){t.push(r?i.replace(sd,"$1"):n||e)})),t}));var ud=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i},ld=ud,dd=Qa,hd=Ql,pd=Pa?Pa.prototype:void 0,fd=pd?pd.toString:void 0;var vd=function e(t){if("string"==typeof t)return t;if(dd(t))return ld(t,e)+"";if(hd(t))return fd?fd.call(t):"";var n=t+"";return"0"==n&&1/t==-Infinity?"-0":n},gd=vd;var md=Qa,yd=nd,_d=cd,bd=function(e){return null==e?"":gd(e)};var wd=function(e,t){return md(e)?e:yd(e,t)?[e]:_d(bd(e))},Sd=Ql;var kd=function(e){if("string"==typeof e||Sd(e))return e;var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t},Ed=wd,Md=kd;var Cd=function(e,t){for(var n=0,r=(t=Ed(t,e)).length;null!=e&&n<r;)e=e[Md(t[n++])];return n&&n==r?e:void 0},Td=Cd;var Od=wd,jd=Ka,Pd=Qa,xd=ts,Ad=ns,Dd=kd;var Ld=function(e,t,n){for(var r=-1,i=(t=Od(t,e)).length,o=!1;++r<i;){var a=Dd(t[r]);if(!(o=null!=e&&n(e,a)))break;e=e[a]}return o||++r!=i?o:!!(i=null==e?0:e.length)&&Ad(i)&&xd(a,i)&&(Pd(e)||jd(e))},Nd=function(e,t){return null!=e&&t in Object(e)},Id=Ld;var Rd=Il,Bd=function(e,t,n){var r=null==e?void 0:Td(e,t);return void 0===r?n:r},Fd=function(e,t){return null!=e&&Id(e,t,Nd)},Ud=nd,Vd=Vl,$d=ql,Gd=kd;var qd=function(e){return e};var Yd=Cd;var zd=function(e){return function(t){return null==t?void 0:t[e]}},Jd=function(e){return function(t){return Yd(t,e)}},Wd=nd,Hd=kd;var Kd=Wl,Qd=function(e,t){return Ud(e)&&Vd(t)?$d(Gd(e),t):function(n){var r=Bd(n,e);return void 0===r&&r===t?Fd(n,e):Rd(t,r,3)}},Xd=qd,Zd=Qa,eh=function(e){return Wd(e)?zd(Hd(e)):Jd(e)};var th=function(e){return"function"==typeof e?e:null==e?Xd:"object"==typeof e?Zd(e)?Qd(e[0],e[1]):Kd(e):eh(e)},nh=Sa,rh=Ys,ih=th,oh=Qa;var ah=function(e,t){return(oh(e)?nh:rh)(e,ih(t))},sh=Gs,ch=Ls;var uh=function(e,t){var n=-1,r=ch(e)?Array(e.length):[];return sh(e,(function(e,i,o){r[++n]=t(e,i,o)})),r};var lh=Ql;var dh=function(e,t){if(e!==t){var n=void 0!==e,r=null===e,i=e==e,o=lh(e),a=void 0!==t,s=null===t,c=t==t,u=lh(t);if(!s&&!u&&!o&&e>t||o&&a&&c&&!s&&!u||r&&a&&c||!n&&c||!i)return 1;if(!r&&!o&&!u&&e<t||u&&n&&i&&!r&&!o||s&&n&&i||!a&&i||!c)return-1}return 0};var hh=function(e,t,n){for(var r=-1,i=e.criteria,o=t.criteria,a=i.length,s=n.length;++r<a;){var c=dh(i[r],o[r]);if(c)return r>=s?c:c*("desc"==n[r]?-1:1)}return e.index-t.index},ph=ud,fh=Cd,vh=th,gh=uh,mh=function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e},yh=cs,_h=hh,bh=qd,wh=Qa;var Sh=function(e,t,n){t=t.length?ph(t,(function(e){return wh(e)?function(t){return fh(t,1===e.length?e[0]:e)}:e})):[bh];var r=-1;t=ph(t,yh(vh));var i=gh(e,(function(e,n,i){return{criteria:ph(t,(function(t){return t(e)})),index:++r,value:e}}));return mh(i,(function(e,t){return _h(e,t,n)}))},kh=Sh,Eh=Qa;var Mh=function(e,t,n,r){return null==e?[]:(Eh(t)||(t=null==t?[]:[t]),Eh(n=r?void 0:n)||(n=null==n?[]:[n]),kh(e,t,n))},Ch=function(e,t,n){return!0===jh(e.local,t,n)},Th=function(e,t,n){return e.local.streams&&e.local.streams[t]&&e.local.streams[t].stream&&e.local.streams[t].stream["get".concat("video"===n?"Video":"Audio","Tracks")]()[0]},Oh=function(e,t,n,r){var i=Ph(e,t,n,r);return i&&i.pendingTrack},jh=function(e,t,n){if(!e)return!1;var r=function(e){switch(e){case"avatar":return!0;case"staged":return e;default:return!!e}},i=e.public.subscribedTracks;return i&&i[t]?-1===["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo"].indexOf(n)&&i[t].custom?[!0,"staged"].includes(i[t].custom)?r(i[t].custom):r(i[t].custom[n]):r(i[t][n]):!i||r(i.ALL)},Ph=function(e,t,n,r){var i=Mh(ah(e.streams,(function(e){return e.participantId===t&&e.type===n&&e.pendingTrack&&e.pendingTrack.kind===r})),"starttime","desc");return i&&i[0]},xh=function(e,t){var n=e.local.public.customTracks;if(n&&n[t])return n[t].track};function Ah(e){for(var t=store.getState(),n=0,r=["cam","screen"];n<r.length;n++)for(var i=r[n],o=0,a=["video","audio"];o<a.length;o++){var s=a[o],c="cam"===i?s:"screen".concat(s.charAt(0).toUpperCase()+s.slice(1)),u=e.tracks[c];if(u){var l=e.local?Th(t,i,s):Oh(t,e.session_id,i,s);"playable"===u.state&&(u.track=l),u.persistentTrack=l}}}function Dh(e){try{var t=store.getState();for(var n in e.tracks)if(!Lh(n)){var r=e.tracks[n].kind;if(r){var i=e.tracks[n];if(i){var o=e.local?xh(t,n):Oh(t,e.session_id,n,r);"playable"===i.state&&(e.tracks[n].track=o),i.persistentTrack=o}}else console.error("unknown type for custom track")}}catch(e){console.error(e)}}function Lh(e){return["video","audio","screenVideo","screenAudio"].includes(e)}function Nh(e,t){var n=store.getState();if(e.local){if(e.audio)try{e.audioTrack=n.local.streams.cam.stream.getAudioTracks()[0],e.audioTrack||(e.audio=!1)}catch(e){}if(e.video)try{e.videoTrack=n.local.streams.cam.stream.getVideoTracks()[0],e.videoTrack||(e.video=!1)}catch(e){}if(e.screen)try{e.screenVideoTrack=n.local.streams.screen.stream.getVideoTracks()[0],e.screenAudioTrack=n.local.streams.screen.stream.getAudioTracks()[0],e.screenVideoTrack||e.screenAudioTrack||(e.screen=!1)}catch(e){}}else{var r=!0;try{var i=n.participants[e.session_id];i&&i.public&&i.public.rtcType&&"peer-to-peer"===i.public.rtcType.impl&&i.private&&!["connected","completed"].includes(i.private.peeringState)&&(r=!1)}catch(e){console.error(e)}if(!r)return e.audio=!1,e.audioTrack=!1,e.video=!1,e.videoTrack=!1,e.screen=!1,void(e.screenTrack=!1);try{n.streams;if(e.audio&&Ch(n,e.session_id,"cam-audio")){var o=Oh(n,e.session_id,"cam","audio");o&&(t&&t.audioTrack&&t.audioTrack.id===o.id?e.audioTrack=o:o.muted||(e.audioTrack=o)),e.audioTrack||(e.audio=!1)}if(e.video&&Ch(n,e.session_id,"cam-video")){var a=Oh(n,e.session_id,"cam","video");a&&(t&&t.videoTrack&&t.videoTrack.id===a.id?e.videoTrack=a:a.muted||(e.videoTrack=a)),e.videoTrack||(e.video=!1)}if(e.screen&&Ch(n,e.session_id,"screen-audio")){var s=Oh(n,e.session_id,"screen","audio");s&&(t&&t.screenAudioTrack&&t.screenAudioTrack.id===s.id?e.screenAudioTrack=s:s.muted||(e.screenAudioTrack=s))}if(e.screen&&Ch(n,e.session_id,"screen-video")){var c=Oh(n,e.session_id,"screen","video");c&&(t&&t.screenVideoTrack&&t.screenVideoTrack.id===c.id?e.screenVideoTrack=c:c.muted||(e.screenVideoTrack=c))}e.screenVideoTrack||e.screenAudioTrack||(e.screen=!1)}catch(e){console.error("unexpected error matching up tracks",e)}}}function Ih(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Rh(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rh(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Rh(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Bh=new Map,Fh=null;function Uh(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Vh(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Vh(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Vh(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var $h=new Map,Gh=null,qh=3e3;function Yh(e){Jh()?function(e){Bh.has(e)||(Bh.set(e,{}),navigator.mediaDevices.enumerateDevices().then((function(t){Bh.has(e)&&(Bh.get(e).lastDevicesString=JSON.stringify(t),Fh||(Fh=function(){var e=n((function*(){var e,t=yield navigator.mediaDevices.enumerateDevices(),n=Ih(Bh.keys());try{for(n.s();!(e=n.n()).done;){var r=e.value,i=JSON.stringify(t);i!==Bh.get(r).lastDevicesString&&(Bh.get(r).lastDevicesString=i,r(t))}}catch(e){n.e(e)}finally{n.f()}}));return function(){return e.apply(this,arguments)}}(),navigator.mediaDevices.addEventListener("devicechange",Fh)))})))}(e):function(e){$h.has(e)||($h.set(e,{}),navigator.mediaDevices.enumerateDevices().then((function(t){$h.has(e)&&($h.get(e).lastDevicesString=JSON.stringify(t),Gh||(Gh=setInterval(n((function*(){var e,t=yield navigator.mediaDevices.enumerateDevices(),n=Uh($h.keys());try{for(n.s();!(e=n.n()).done;){var r=e.value,i=JSON.stringify(t);i!==$h.get(r).lastDevicesString&&($h.get(r).lastDevicesString=i,r(t))}}catch(e){n.e(e)}finally{n.f()}})),qh)))})))}(e)}function zh(e){Jh()?function(e){Bh.has(e)&&(Bh.delete(e),0===Bh.size&&Fh&&(navigator.mediaDevices.removeEventListener("devicechange",Fh),Fh=null))}(e):function(e){$h.has(e)&&($h.delete(e),0===$h.size&&Gh&&(clearInterval(Gh),Gh=null))}(e)}function Jh(){return Ro()||void 0!==navigator.mediaDevices.ondevicechange}var Wh=new Set;function Hh(e,t){var n=t.isLocalScreenVideo;return e&&"live"===e.readyState&&!function(e,t){return(!t.isLocalScreenVideo||"Chrome"!==Jo())&&e.muted&&!Wh.has(e.id)}(e,{isLocalScreenVideo:n})}var Kh,Qh=["preserveIframe"];function Xh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Zh(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xh(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xh(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ep(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=h(e);if(t){var i=h(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d(this,n)}}function tp(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return np(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return np(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function np(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var rp="video",ip="voice",op=Ro()?{data:{}}:{data:{},topology:"none"},ap={present:0,hidden:0},sp={maxBitrate:{min:1e5,max:25e5},maxFramerate:{min:1,max:30},scaleResolutionDownBy:{min:1,max:8}},cp=Object.keys(sp),up=["state","volume","simulcastEncodings"],lp={androidInCallNotification:{title:"string",subtitle:"string",iconName:"string",disableForCustomOverride:"boolean"},disableAutoDeviceManagement:{audio:"boolean",video:"boolean"}},dp={id:{iconPath:"string",iconPathDarkMode:"string",label:"string",tooltip:"string"}},hp={id:{allow:"string",controlledBy:"'*' | 'owners' | string[]",csp:"string",iconURL:"string",label:"string",loading:"'eager' | 'lazy'",location:"'main' | 'sidebar'",name:"string",referrerPolicy:"string",sandbox:"string",src:"string",srcdoc:"string",shared:"string[] | 'owners' | boolean"}},pp={customIntegrations:{validate:Pp,help:Op()},customTrayButtons:{validate:jp,help:"customTrayButtons should be a dictionary of the type ".concat(JSON.stringify(dp))},url:{validate:function(e){return"string"==typeof e},help:"url should be a string"},baseUrl:{validate:function(e){return"string"==typeof e},help:"baseUrl should be a string"},token:{validate:function(e){return"string"==typeof e},help:"token should be a string",queryString:"t"},dailyConfig:{validate:function(e,t){try{return t.validateDailyConfig(e),window._dailyConfig||(window._dailyConfig={}),window._dailyConfig.experimentalGetUserMediaConstraintsModify=e.experimentalGetUserMediaConstraintsModify,window._dailyConfig.userMediaVideoConstraints=e.userMediaVideoConstraints,window._dailyConfig.userMediaAudioConstraints=e.userMediaAudioConstraints,window._dailyConfig.callObjectBundleUrlOverride=e.callObjectBundleUrlOverride,window._dailyConfig.proxyUrl=e.proxyUrl,window._dailyConfig.iceConfig=e.iceConfig,!0}catch(e){console.error("Failed to validate dailyConfig",e)}return!1},help:"Unsupported dailyConfig. Check error logs for detailed info."},reactNativeConfig:{validate:function(e){return xp(e,lp)},help:"reactNativeConfig should look like ".concat(JSON.stringify(lp),", all fields optional")},lang:{validate:function(e){return["de","en-us","en","es","fi","fr","it","jp","ka","nl","no","pl","pt","pt-BR","ru","sv","tr","user"].includes(e)},help:"language not supported. Options are: de, en-us, en, es, fi, fr, it, jp, ka, nl, no, pl, pt, pt-BR, ru, sv, tr, user"},userName:!0,userData:{validate:function(e){try{return Sp(e),!0}catch(e){return console.error(e),!1}},help:"invalid userData type provided"},startVideoOff:!0,startAudioOff:!0,activeSpeakerMode:!0,showLeaveButton:!0,showLocalVideo:!0,showParticipantsBar:!0,showFullscreenButton:!0,showUserNameChangeUI:!0,iframeStyle:!0,customLayout:!0,cssFile:!0,cssText:!0,bodyClass:!0,videoSource:{validate:function(e,t){return t._preloadCache.videoDeviceId=e,!0}},audioSource:{validate:function(e,t){return t._preloadCache.audioDeviceId=e,!0}},subscribeToTracksAutomatically:{validate:function(e,t){return t._preloadCache.subscribeToTracksAutomatically=e,!0}},theme:{validate:function(e){var t=["accent","accentText","background","backgroundAccent","baseText","border","mainAreaBg","mainAreaBgAccent","mainAreaText","supportiveText"],n=function(e){for(var n=0,r=Object.keys(e);n<r.length;n++){var i=r[n];if(!t.includes(i))return console.error('unsupported color "'.concat(i,'". Valid colors: ').concat(t.join(", "))),!1;if(!e[i].match(/^#[0-9a-f]{6}|#[0-9a-f]{3}$/i))return console.error("".concat(i,' theme color should be provided in valid hex color format. Received: "').concat(e[i],'"')),!1}return!0};return"object"===i(e)&&("light"in e&&"dark"in e||"colors"in e)?"light"in e&&"dark"in e?"colors"in e.light?"colors"in e.dark?n(e.light.colors)&&n(e.dark.colors):(console.error('Dark theme is missing "colors" property.',e),!1):(console.error('Light theme is missing "colors" property.',e),!1):n(e.colors):(console.error('Theme must contain either both "light" and "dark" properties, or "colors".',e),!1)},help:"unsupported theme configuration. Check error logs for detailed info."},layoutConfig:{validate:function(e){if("grid"in e){var t=e.grid;if("maxTilesPerPage"in t){if(!Number.isInteger(t.maxTilesPerPage))return console.error("grid.maxTilesPerPage should be an integer. You passed ".concat(t.maxTilesPerPage,".")),!1;if(t.maxTilesPerPage>49)return console.error("grid.maxTilesPerPage can't be larger than 49 without sacrificing browser performance. Please contact us at https://www.daily.co/contact to talk about your use case."),!1}if("minTilesPerPage"in t){if(!Number.isInteger(t.minTilesPerPage))return console.error("grid.minTilesPerPage should be an integer. You passed ".concat(t.minTilesPerPage,".")),!1;if(t.minTilesPerPage<1)return console.error("grid.minTilesPerPage can't be lower than 1."),!1;if("maxTilesPerPage"in t&&t.minTilesPerPage>t.maxTilesPerPage)return console.error("grid.minTilesPerPage can't be higher than grid.maxTilesPerPage."),!1}}return!0},help:"unsupported layoutConfig. Check error logs for detailed info."},receiveSettings:{validate:function(e){return kp(e,{allowAllParticipantsKey:!1})},help:Tp({allowAllParticipantsKey:!1})},sendSettings:{validate:function(e,t){return!!function(e,t){try{return t.validateUpdateSendSettings(e),!0}catch(e){return console.error("Failed to validate send settings",e),!1}}(e,t)&&(t._preloadCache.sendSettings=e,!0)},help:"Invalid sendSettings provided. Check error logs for detailed info."},inputSettings:{validate:function(e,t){return!!Ep(e)&&(t._preloadCache.inputSettings||(t._preloadCache.inputSettings={}),Mp(e),e.audio&&(t._preloadCache.inputSettings.audio=e.audio),e.video&&(t._preloadCache.inputSettings.video=e.video),!0)},help:Cp()},layout:{validate:function(e){return"custom-v1"===e||"browser"===e||"none"===e},help:'layout may only be set to "custom-v1"',queryString:"layout"},emb:{queryString:"emb"},embHref:{queryString:"embHref"},dailyJsVersion:{queryString:"dailyJsVersion"},proxy:{queryString:"proxy"},strictMode:!0},fp={styles:{validate:function(e){for(var t in e)if("cam"!==t&&"screen"!==t)return!1;if(e.cam)for(var n in e.cam)if("div"!==n&&"video"!==n)return!1;if(e.screen)for(var r in e.screen)if("div"!==r&&"video"!==r)return!1;return!0},help:"styles format should be a subset of: { cam: {div: {}, video: {}}, screen: {div: {}, video: {}} }"},setSubscribedTracks:{validate:function(e,t){if(t._preloadCache.subscribeToTracksAutomatically)return!1;var n=[!0,!1,"staged"];if(n.includes(e)||!Ro()&&"avatar"===e)return!0;var r=["audio","video","screenAudio","screenVideo","rmpAudio","rmpVideo"];return function e(t){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var o in t)if("custom"===o){if(!n.includes(t[o])&&!e(t[o],!0))return!1}else{var a=!i&&!r.includes(o),s=!n.includes(t[o]);if(a||s)return!1}return!0}(e)},help:"setSubscribedTracks cannot be used when setSubscribeToTracksAutomatically is enabled, and should be of the form: "+"true".concat(Ro()?"":" | 'avatar'"," | false | 'staged' | { [audio: true|false|'staged'], [video: true|false|'staged'], [screenAudio: true|false|'staged'], [screenVideo: true|false|'staged'] }")},setAudio:!0,setVideo:!0,setScreenShare:{validate:function(e){return!1===e},help:"setScreenShare must be false, as it's only meant for stopping remote participants' screen shares"},eject:!0,updatePermissions:{validate:function(e){for(var t=0,n=Object.entries(e);t<n.length;t++){var r=v(n[t],2),i=r[0],o=r[1];switch(i){case"hasPresence":if("boolean"!=typeof o)return!1;break;case"canSend":if(o instanceof Set||o instanceof Array){var a,s=["video","audio","screenVideo","screenAudio","customVideo","customAudio"],c=tp(o);try{for(c.s();!(a=c.n()).done;){var u=a.value;if(!s.includes(u))return!1}}catch(e){c.e(e)}finally{c.f()}}else if("boolean"!=typeof o)return!1;o instanceof Array&&(e.canSend=new Set(o));break;case"canAdmin":if(o instanceof Set||o instanceof Array){var l,d=["participants","streaming","transcription"],h=tp(o);try{for(h.s();!(l=h.n()).done;){var p=l.value;if(!d.includes(p))return!1}}catch(e){h.e(e)}finally{h.f()}}else if("boolean"!=typeof o)return!1;o instanceof Array&&(e.canAdmin=new Set(o));break;default:return!1}}return!0},help:"updatePermissions can take hasPresence, canSend, and canAdmin permissions. hasPresence must be a boolean. canSend can be a boolean or an Array or Set of media types (video, audio, screenVideo, screenAudio, customVideo, customAudio). canAdmin can be a boolean or an Array or Set of admin types (participants, streaming, transcription)."}},vp=function(t){l(z,y);var o,a,u,d,h,f,g,m,_,b,w,S,k,E,M,C,T,O,j,P,x,A,D,L,N,I,R,B,F,U,V,$,G,q,Y=ep(z);function z(e){var t,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r(this,z),p(c(t=Y.call(this)),"startListeningForDeviceChanges",(function(){Yh(t.handleDeviceChange)})),p(c(t),"stopListeningForDeviceChanges",(function(){zh(t.handleDeviceChange)})),p(c(t),"handleDeviceChange",(function(e){e=e.map((function(e){return JSON.parse(JSON.stringify(e))})),t.emit(Pi,{action:Pi,availableDevices:e})})),p(c(t),"handleNativeAppActiveStateChange",(function(e){t.disableReactNativeAutoDeviceManagement("video")||(e?t.camUnmutedBeforeLosingNativeActiveState&&t.setLocalVideo(!0):(t.camUnmutedBeforeLosingNativeActiveState=t.localVideo(),t.camUnmutedBeforeLosingNativeActiveState&&t.setLocalVideo(!1)))})),p(c(t),"handleNativeAudioFocusChange",(function(e){t.disableReactNativeAutoDeviceManagement("audio")||(t._hasNativeAudioFocus=e,t.toggleParticipantAudioBasedOnNativeAudioFocus(),t._hasNativeAudioFocus?t.micUnmutedBeforeLosingNativeAudioFocus&&t.setLocalAudio(!0):(t.micUnmutedBeforeLosingNativeAudioFocus=t.localAudio(),t.setLocalAudio(!1)))})),p(c(t),"handleNativeSystemScreenCaptureStop",(function(){t.stopScreenShare()})),t.strictMode=void 0===i.strictMode||i.strictMode,Kh){if(t._logDuplicateInstanceAttempt(),t.strictMode)throw new Error("Duplicate DailyIframe instances are not allowed")}else n=c(t),Kh=n;if(i.dailyJsVersion=z.version(),t._iframe=e,t._callObjectMode="none"===i.layout&&!t._iframe,t._preloadCache={subscribeToTracksAutomatically:!0,audioDeviceId:null,videoDeviceId:null,outputDeviceId:null,inputSettings:null,sendSettings:null,videoTrackForNetworkConnectivityTest:null,videoTrackForConnectionQualityTest:null},t._callObjectMode&&(window._dailyPreloadCache=t._preloadCache),void 0!==i.showLocalVideo?t._callObjectMode?console.error("showLocalVideo is not available in call object mode"):t._showLocalVideo=!!i.showLocalVideo:t._showLocalVideo=!0,void 0!==i.showParticipantsBar?t._callObjectMode?console.error("showParticipantsBar is not available in call object mode"):t._showParticipantsBar=!!i.showParticipantsBar:t._showParticipantsBar=!0,void 0!==i.customIntegrations?t._callObjectMode?console.error("customIntegrations is not available in call object mode"):t._customIntegrations=i.customIntegrations:t._customIntegrations={},void 0!==i.customTrayButtons?t._callObjectMode?console.error("customTrayButtons is not available in call object mode"):t._customTrayButtons=i.customTrayButtons:t._customTrayButtons={},void 0!==i.activeSpeakerMode?t._callObjectMode?console.error("activeSpeakerMode is not available in call object mode"):t._activeSpeakerMode=!!i.activeSpeakerMode:t._activeSpeakerMode=!1,i.receiveSettings?t._callObjectMode?t._receiveSettings=i.receiveSettings:console.error("receiveSettings is only available in call object mode"):t._receiveSettings={},t.validateProperties(i),t.properties=Zh({},i),t._preloadCache.inputSettings||(t._preloadCache.inputSettings={}),i.inputSettings&&i.inputSettings.audio&&(t._preloadCache.inputSettings.audio=i.inputSettings.audio),i.inputSettings&&i.inputSettings.video&&(t._preloadCache.inputSettings.video=i.inputSettings.video),t._callObjectLoader=t._callObjectMode?new va:null,t._callState=Ir,t._isPreparingToJoin=!1,t._accessState={access:Hr},t._meetingSessionSummary={},t._finalSummaryOfPrevSession={},t._meetingSessionState=Lp(op,t._callObjectMode),t._nativeInCallAudioMode=rp,t._participants={},t._participantCounts=ap,t._rmpPlayerState={},t._waitingParticipants={},t._inputEventsOn={},t._network={threshold:"good",quality:100},t._activeSpeaker={},t._callFrameId=de(),t._localAudioLevel=0,t._remoteParticipantsAudioLevel={},t._messageChannel=Ro()?new ia:new na,t._iframe&&(t._iframe.requestFullscreen?t._iframe.addEventListener("fullscreenchange",(function(){document.fullscreenElement===t._iframe?(t.emit(lo,{action:lo}),t.sendMessageToCallMachine({action:lo})):(t.emit(ho,{action:ho}),t.sendMessageToCallMachine({action:ho}))})):t._iframe.webkitRequestFullscreen&&t._iframe.addEventListener("webkitfullscreenchange",(function(){document.webkitFullscreenElement===t._iframe?(t.emit(lo,{action:lo}),t.sendMessageToCallMachine({action:lo})):(t.emit(ho,{action:ho}),t.sendMessageToCallMachine({action:ho}))}))),Ro()){var o=t.nativeUtils();o.addAudioFocusChangeListener&&o.removeAudioFocusChangeListener&&o.addAppActiveStateChangeListener&&o.removeAppActiveStateChangeListener&&o.addSystemScreenCaptureStopListener&&o.removeSystemScreenCaptureStopListener||console.warn("expected (add|remove)(AudioFocusChange|AppActiveStateChange|SystemScreenCaptureStop)Listener to be available in React Native"),t._hasNativeAudioFocus=!0,o.addAudioFocusChangeListener(t.handleNativeAudioFocusChange),o.addAppActiveStateChangeListener(t.handleNativeAppActiveStateChange),o.addSystemScreenCaptureStopListener(t.handleNativeSystemScreenCaptureStop)}return t._callObjectMode&&t.startListeningForDeviceChanges(),t._messageChannel.addListenerForMessagesFromCallMachine(t.handleMessageFromCallMachine,t._callFrameId,c(t)),t}return s(z,[{key:"destroy",value:(q=n((function*(){try{yield this.leave()}catch(e){}var e=this._iframe;if(e){var t=e.parentElement;t&&t.removeChild(e)}if(this._messageChannel.removeListener(this.handleMessageFromCallMachine),Ro()){var n=this.nativeUtils();n.removeAudioFocusChangeListener(this.handleNativeAudioFocusChange),n.removeAppActiveStateChangeListener(this.handleNativeAppActiveStateChange),n.removeSystemScreenCaptureStopListener(this.handleNativeSystemScreenCaptureStop)}this._callObjectMode&&this.stopListeningForDeviceChanges(),this.resetMeetingDependentVars(),this._destroyed=!0;try{this.emit("call-instance-destroyed",{action:"call-instance-destroyed"})}catch(e){console.log("could not emit call-instance-destroyed")}this.strictMode&&(this._callFrameId=void 0),Kh=void 0})),function(){return q.apply(this,arguments)})},{key:"isDestroyed",value:function(){return!!this._destroyed}},{key:"loadCss",value:function(e){var t=e.bodyClass,n=e.cssFile,r=e.cssText;return bp(),this.sendMessageToCallMachine({action:"load-css",cssFile:this.absoluteUrl(n),bodyClass:t,cssText:r}),this}},{key:"iframe",value:function(){return bp(),this._iframe}},{key:"meetingState",value:function(){return this._callState}},{key:"accessState",value:function(){return yp(this._callObjectMode,"accessState()"),this._accessState}},{key:"participants",value:function(){return this._participants}},{key:"participantCounts",value:function(){return this._participantCounts}},{key:"waitingParticipants",value:function(){return yp(this._callObjectMode,"waitingParticipants()"),this._waitingParticipants}},{key:"validateParticipantProperties",value:function(e,t){for(var n in t){if(!fp[n])throw new Error("unrecognized updateParticipant property ".concat(n));if(fp[n].validate&&!fp[n].validate(t[n],this,this._participants[e]))throw new Error(fp[n].help)}}},{key:"updateParticipant",value:function(e,t){return this._participants.local&&this._participants.local.session_id===e&&(e="local"),e&&t&&(this.validateParticipantProperties(e,t),this.sendMessageToCallMachine({action:"update-participant",id:e,properties:t})),this}},{key:"updateParticipants",value:function(e){var t=this._participants.local&&this._participants.local.session_id;for(var n in e)n===t&&(n="local"),n&&e[n]&&this.validateParticipantProperties(n,e[n]);return this.sendMessageToCallMachine({action:"update-participants",participants:e}),this}},{key:"updateWaitingParticipant",value:(G=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(yp(this._callObjectMode,"updateWaitingParticipant()"),mp(this._callState,"updateWaitingParticipant()"),"string"!=typeof t||"object"!==i(n))throw new Error("updateWaitingParticipant() must take an id string and a updates object");return new Promise((function(r,i){e.sendMessageToCallMachine({action:"daily-method-update-waiting-participant",id:t,updates:n},(function(e){e.error&&i(e.error),e.id||i(new Error("unknown error in updateWaitingParticipant()")),r({id:e.id})}))}))})),function(){return G.apply(this,arguments)})},{key:"updateWaitingParticipants",value:($=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(yp(this._callObjectMode,"updateWaitingParticipants()"),mp(this._callState,"updateWaitingParticipants()"),"object"!==i(t))throw new Error("updateWaitingParticipants() must take a mapping between ids and update objects");return new Promise((function(n,r){e.sendMessageToCallMachine({action:"daily-method-update-waiting-participants",updatesById:t},(function(e){e.error&&r(e.error),e.ids||r(new Error("unknown error in updateWaitingParticipants()")),n({ids:e.ids})}))}))})),function(){return $.apply(this,arguments)})},{key:"requestAccess",value:(V=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.access,r=void 0===n?{level:Kr}:n,i=t.name,o=void 0===i?"":i;return yp(this._callObjectMode,"requestAccess()"),mp(this._callState,"requestAccess()"),new Promise((function(t,n){e.sendMessageToCallMachine({action:"daily-method-request-access",access:r,name:o},(function(e){e.error&&n(e.error),e.access||n(new Error("unknown error in requestAccess()")),t({access:e.access,granted:e.granted})}))}))})),function(){return V.apply(this,arguments)})},{key:"localAudio",value:function(){return this._participants.local?this._participants.local.audio:null}},{key:"localVideo",value:function(){return this._participants.local?this._participants.local.video:null}},{key:"setLocalAudio",value:function(e){return this.sendMessageToCallMachine({action:"local-audio",state:e}),this}},{key:"setLocalVideo",value:function(e){return this.sendMessageToCallMachine({action:"local-video",state:e}),this}},{key:"getReceiveSettings",value:(U=n((function*(e){var t=this,n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).showInheritedValues,r=void 0!==n&&n;if(yp(this._callObjectMode,"getReceiveSettings()"),!this._dailyMainExecuted)return this._receiveSettings;switch(i(e)){case"string":return new Promise((function(n){t.sendMessageToCallMachine({action:"get-single-participant-receive-settings",id:e,showInheritedValues:r},(function(e){n(e.receiveSettings)}))}));case"undefined":return this._receiveSettings;default:throw new Error('first argument to getReceiveSettings() must be a participant id (or "base"), or there should be no arguments')}})),function(e){return U.apply(this,arguments)})},{key:"updateReceiveSettings",value:(F=n((function*(e){var t=this;if(yp(this._callObjectMode,"updateReceiveSettings()"),!kp(e,{allowAllParticipantsKey:!0}))throw new Error(Tp({allowAllParticipantsKey:!0}));return mp(this._callState,"updateReceiveSettings()","To specify receive settings earlier, use the receiveSettings config property."),new Promise((function(n){t.sendMessageToCallMachine({action:"update-receive-settings",receiveSettings:e},(function(e){n({receiveSettings:e.receiveSettings})}))}))})),function(e){return F.apply(this,arguments)})},{key:"_prepInputSettingsToPresentToUser",value:function(e){var t,n,r,i,o,a,s,c;if(e){var u={},l="none"===(null===(t=e.audio)||void 0===t||null===(n=t.processor)||void 0===n?void 0:n.type)&&(null===(r=e.audio)||void 0===r||null===(i=r.processor)||void 0===i?void 0:i._isDefaultWhenNone);if(e.audio&&!l){var d=Zh({},e.audio.processor);delete d._isDefaultWhenNone,u.audio=Zh(Zh({},e.audio),{},{processor:d})}var h="none"===(null===(o=e.video)||void 0===o||null===(a=o.processor)||void 0===a?void 0:a.type)&&(null===(s=e.video)||void 0===s||null===(c=s.processor)||void 0===c?void 0:c._isDefaultWhenNone);if(e.video&&!h){var p=Zh({},e.video.processor);delete p._isDefaultWhenNone,u.video=Zh(Zh({},e.video),{},{processor:p})}return u}}},{key:"getInputSettings",value:function(){var e=this;return new Promise((function(t){t(e._getInputSettings())}))}},{key:"_getInputSettings",value:function(){var e,t,n,r,i,o,a,s,c={processor:{type:"none",_isDefaultWhenNone:!0}};this._inputSettings?(e=(null===(n=this._inputSettings)||void 0===n?void 0:n.video)||c,t=(null===(r=this._inputSettings)||void 0===r?void 0:r.audio)||c):(e=(null===(i=this._preloadCache)||void 0===i||null===(o=i.inputSettings)||void 0===o?void 0:o.video)||c,t=(null===(a=this._preloadCache)||void 0===a||null===(s=a.inputSettings)||void 0===s?void 0:s.audio)||c);var u={audio:t,video:e};return this._prepInputSettingsToPresentToUser(u)}},{key:"updateInputSettings",value:(B=n((function*(e){var t=this;return Ep(e)?(e&&(this._preloadCache.inputSettings||(this._preloadCache.inputSettings={}),Mp(e),e.audio&&(this._preloadCache.inputSettings.audio=e.audio),e.video&&(this._preloadCache.inputSettings.video=e.video)),Ep(e)?this._callObjectMode&&this.needsLoad()?this._getInputSettings():new Promise((function(n,r){t.sendMessageToCallMachine({action:"update-input-settings",inputSettings:e},(function(e){e.error?r(e.error):n({inputSettings:t._prepInputSettingsToPresentToUser(e.inputSettings)})}))})):this._getInputSettings()):(console.error(Cp()),Promise.reject(Cp()))})),function(e){return B.apply(this,arguments)})},{key:"setBandwidth",value:function(e){var t=e.kbs,n=e.trackConstraints;if(bp(),this._dailyMainExecuted)return this.sendMessageToCallMachine({action:"set-bandwidth",kbs:t,trackConstraints:n}),this}},{key:"getDailyLang",value:function(){var e=this;if(bp(),this._dailyMainExecuted)return new Promise(function(){var t=n((function*(t){e.sendMessageToCallMachine({action:"get-daily-lang"},(function(e){delete e.action,delete e.callbackStamp,t(e)}))}));return function(e){return t.apply(this,arguments)}}())}},{key:"setDailyLang",value:function(e){return bp(),this.sendMessageToCallMachine({action:"set-daily-lang",lang:e}),this}},{key:"setProxyUrl",value:function(e){return this.sendMessageToCallMachine({action:"set-proxy-url",proxyUrl:e}),this}},{key:"setIceConfig",value:function(e){return this.sendMessageToCallMachine({action:"set-ice-config",iceConfig:e}),this}},{key:"meetingSessionSummary",value:function(){return[Vr,$r].includes(this._callState)?this._finalSummaryOfPrevSession:this._meetingSessionSummary}},{key:"getMeetingSession",value:(R=n((function*(){var e=this;return console.warn("getMeetingSession() is deprecated: use meetingSessionSummary(), which will return immediately"),mp(this._callState,"getMeetingSession()"),new Promise((function(t){e.sendMessageToCallMachine({action:"get-meeting-session"},(function(e){delete e.action,delete e.callbackStamp,delete e.callFrameId,t(e)}))}))})),function(){return R.apply(this,arguments)})},{key:"meetingSessionState",value:function(){if(this._callState!==Ur)throw new Error("meetingSessionState() is only available when joined");return this._meetingSessionState}},{key:"setMeetingSessionData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"replace";if(yp(this._callObjectMode,"setMeetingSessionData()"),this._callState!==Ur)throw new Error("setMeetingSessionData() is only available when joined");try{!function(e,t){new da({data:e,mergeStrategy:t})}(e,t)}catch(e){throw console.error(e),e}try{this.sendMessageToCallMachine({action:"set-session-data",data:e,mergeStrategy:t})}catch(e){throw new Error("Error setting meeting session data: ".concat(e))}}},{key:"setUserName",value:function(e,t){var n=this;return this.properties.userName=e,new Promise((function(r){n.sendMessageToCallMachine({action:"set-user-name",name:null!=e?e:"",thisMeetingOnly:Ro()||!!t&&!!t.thisMeetingOnly},(function(e){delete e.action,delete e.callbackStamp,r(e)}))}))}},{key:"setUserData",value:(I=n((function*(e){var t=this;try{Sp(e)}catch(e){throw console.error(e),e}if(this.properties.userData=e,this._dailyMainExecuted)return new Promise((function(n){try{t.sendMessageToCallMachine({action:"set-user-data",userData:e},(function(e){delete e.action,delete e.callbackStamp,delete e.callFrameId,n(e)}))}catch(e){throw new Error("Error setting user data: ".concat(e))}}))})),function(e){return I.apply(this,arguments)})},{key:"validateAudioLevelInterval",value:function(e){if(e&&(e<100||"number"!=typeof e))throw new Error("The interval must be a number greater than or equal to 100 milliseconds.")}},{key:"startLocalAudioLevelObserver",value:function(e){var t=this;if(bp(),this.validateAudioLevelInterval(e),this._dailyMainExecuted)return new Promise((function(n,r){t.sendMessageToCallMachine({action:"start-local-audio-level-observer",interval:e},(function(e){e.error?r({error:e.error}):n()}))}));this._preloadCache.localAudioLevelObserver={enabled:!0,interval:e}}},{key:"stopLocalAudioLevelObserver",value:(N=n((function*(){bp(),this._preloadCache.localAudioLevelObserver=null,this._localAudioLevel=0,this.sendMessageToCallMachine({action:"stop-local-audio-level-observer"})})),function(){return N.apply(this,arguments)})},{key:"startRemoteParticipantsAudioLevelObserver",value:function(e){var t=this;if(bp(),this.validateAudioLevelInterval(e),this._dailyMainExecuted)return new Promise((function(n,r){t.sendMessageToCallMachine({action:"start-remote-participants-audio-level-observer",interval:e},(function(e){e.error?r({error:e.error}):n()}))}));this._preloadCache.remoteParticipantsAudioLevelObserver={enabled:!0,interval:e}}},{key:"stopRemoteParticipantsAudioLevelObserver",value:(L=n((function*(){bp(),this._preloadCache.remoteParticipantsAudioLevelObserver=null,this._remoteParticipantsAudioLevel={},this.sendMessageToCallMachine({action:"stop-remote-participants-audio-level-observer"})})),function(){return L.apply(this,arguments)})},{key:"startCamera",value:(D=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(yp(this._callObjectMode,"startCamera()"),[Fr,Ur].includes(this._callState))throw new Error("startCamera() not supported after joining a meeting: did you mean to use setLocalAudio() and/or setLocalVideo() instead?");if(this.needsLoad())try{yield this.load(t)}catch(e){return Promise.reject(e)}else{if(this._didPreAuth){if(t.url&&t.url!==this.properties.url)return console.error("url in startCamera() is different than the one used in preAuth()"),Promise.reject();if(t.token&&t.token!==this.properties.token)return console.error("token in startCamera() is different than the one used in preAuth()"),Promise.reject()}this.validateProperties(t),this.properties=Zh(Zh({},this.properties),t)}return new Promise((function(t){e.sendMessageToCallMachine({action:"start-camera",properties:gp(e.properties),preloadCache:gp(e._preloadCache)},(function(e){delete e.action,delete e.callbackStamp,t(e)}))}))})),function(){return D.apply(this,arguments)})},{key:"validateCustomTrack",value:function(e,t,n){if(n&&n.length>50)throw new Error("Custom track `trackName` must not be more than 50 characters");if(t&&"music"!==t&&"speech"!==t&&!(t instanceof Object))throw new Error("Custom track `mode` must be either `music` | `speech` | `DailyMicAudioModeSettings` or `undefined`");if(!!n&&["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo","customVideoDefaults"].includes(n))throw new Error("Custom track `trackName` must not match a track name already used by daily: cam-audio, cam-video, customVideoDefaults, screen-video, screen-audio, rmpAudio, rmpVideo");if(!(e instanceof MediaStreamTrack))throw new Error("Custom tracks provided must be instances of MediaStreamTrack")}},{key:"startCustomTrack",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{track:track,mode:mode,trackName:trackName};if(bp(),this.validateCustomTrack(t.track,t.mode,t.trackName),this._callState!==Ur)throw new Error("startCustomTrack() is only allowed when joined");return new Promise((function(n,r){window._dailyPreloadCache.customTrack=t.track,t.track=Oo,e.sendMessageToCallMachine({action:"start-custom-track",properties:t},(function(e){e.error?r({error:e.error}):n(e.mediaTag)}))}))}},{key:"stopCustomTrack",value:function(e){var t=this;if(bp(),this._callState!==Ur)throw new Error("stopCustomTrack() is only allowed when joined");return new Promise((function(n){t.sendMessageToCallMachine({action:"stop-custom-track",mediaTag:e},(function(e){n(e.mediaTag)}))}))}},{key:"setCamera",value:function(e){var t=this;if(wp(),this.needsLoad())throw new Error("Before you can invoke setCamera, first you need to invoke one of these methods: join, startCamera, or preAuth");return new Promise((function(n){t.sendMessageToCallMachine({action:"set-camera",cameraDeviceId:e},(function(e){n({device:e.device})}))}))}},{key:"setAudioDevice",value:(A=n((function*(e){return wp(),this.nativeUtils().setAudioDevice(e),{deviceId:yield this.nativeUtils().getAudioDevice()}})),function(e){return A.apply(this,arguments)})},{key:"cycleCamera",value:function(){var e=this;return new Promise((function(t){e.sendMessageToCallMachine({action:"cycle-camera"},(function(e){t({device:e.device})}))}))}},{key:"cycleMic",value:function(){var e=this;return bp(),new Promise((function(t){e.sendMessageToCallMachine({action:"cycle-mic"},(function(e){t({device:e.device})}))}))}},{key:"getCameraFacingMode",value:function(){var e=this;return wp(),new Promise((function(t){e.sendMessageToCallMachine({action:"get-camera-facing-mode"},(function(e){t(e.facingMode)}))}))}},{key:"setInputDevicesAsync",value:(x=n((function*(e){var t=this,n=e.audioDeviceId,r=e.videoDeviceId,i=e.audioSource,o=e.videoSource;return bp(),void 0!==i&&(n=i),void 0!==o&&(r=o),n&&(this._preloadCache.audioDeviceId=n),r&&(this._preloadCache.videoDeviceId=r),this._callObjectMode&&this.needsLoad()?{camera:{deviceId:this._preloadCache.videoDeviceId},mic:{deviceId:this._preloadCache.audioDeviceId},speaker:{deviceId:this._preloadCache.outputDeviceId}}:(n instanceof MediaStreamTrack&&(n=Oo),r instanceof MediaStreamTrack&&(r=Oo),new Promise((function(e){t.sendMessageToCallMachine({action:"set-input-devices",audioDeviceId:n,videoDeviceId:r},(function(n){delete n.action,delete n.callbackStamp,n.returnPreloadCache?e({camera:{deviceId:t._preloadCache.videoDeviceId},mic:{deviceId:t._preloadCache.audioDeviceId},speaker:{deviceId:t._preloadCache.outputDeviceId}}):e(n)}))})))})),function(e){return x.apply(this,arguments)})},{key:"setOutputDeviceAsync",value:(P=n((function*(e){var t=this,n=e.outputDeviceId;return bp(),n&&(this._preloadCache.outputDeviceId=n),this._callObjectMode&&this.needsLoad()?{camera:{deviceId:this._preloadCache.videoDeviceId},mic:{deviceId:this._preloadCache.audioDeviceId},speaker:{deviceId:this._preloadCache.outputDeviceId}}:new Promise((function(e){t.sendMessageToCallMachine({action:"set-output-device",outputDeviceId:n},(function(n){delete n.action,delete n.callbackStamp,n.returnPreloadCache?e({camera:{deviceId:t._preloadCache.videoDeviceId},mic:{deviceId:t._preloadCache.audioDeviceId},speaker:{deviceId:t._preloadCache.outputDeviceId}}):e(n)}))}))})),function(e){return P.apply(this,arguments)})},{key:"getInputDevices",value:(j=n((function*(){var e=this;return this._callObjectMode&&this.needsLoad()?{camera:{deviceId:this._preloadCache.videoDeviceId},mic:{deviceId:this._preloadCache.audioDeviceId},speaker:{deviceId:this._preloadCache.outputDeviceId}}:new Promise((function(t){e.sendMessageToCallMachine({action:"get-input-devices"},(function(n){delete n.action,delete n.callbackStamp,n.returnPreloadCache?t({camera:{deviceId:e._preloadCache.videoDeviceId},mic:{deviceId:e._preloadCache.audioDeviceId},speaker:{deviceId:e._preloadCache.outputDeviceId}}):t(n)}))}))})),function(){return j.apply(this,arguments)})},{key:"nativeInCallAudioMode",value:function(){return wp(),this._nativeInCallAudioMode}},{key:"setNativeInCallAudioMode",value:function(e){if(wp(),[rp,ip].includes(e)){if(e!==this._nativeInCallAudioMode)return this._nativeInCallAudioMode=e,!this.disableReactNativeAutoDeviceManagement("audio")&&this._isCallPendingOrOngoing(this._callState,this._isPreparingToJoin)&&this.nativeUtils().setAudioMode(this._nativeInCallAudioMode),this}else console.error("invalid in-call audio mode specified: ",e)}},{key:"preAuth",value:(O=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(yp(this._callObjectMode,"preAuth()"),[Fr,Ur].includes(this._callState))throw new Error("preAuth() not supported after joining a meeting");if(this.needsLoad()&&(yield this.load(t)),!t.url)throw new Error("preAuth() requires at least a url to be provided");return this.validateProperties(t),this.properties=Zh(Zh({},this.properties),t),new Promise((function(t,n){e.sendMessageToCallMachine({action:"daily-method-preauth",properties:gp(e.properties),preloadCache:gp(e._preloadCache)},(function(r){return r.error?n(r.error):r.access?(e._didPreAuth=!0,void t({access:r.access})):n(new Error("unknown error in preAuth()"))}))}))})),function(){return O.apply(this,arguments)})},{key:"load",value:(T=n((function*(e){var t=this;if(this.needsLoad()){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw new Error("Use after destroy");if(e&&(this.validateProperties(e),this.properties=Zh(Zh({},this.properties),e)),!this._callObjectMode&&!this.properties.url)throw new Error("can't load iframe meeting because url property isn't set");this._updateCallState(Rr);try{this.emit(Si,{action:Si})}catch(e){console.log("could not emit 'loading'",e)}return this._callObjectMode?new Promise((function(e,n){t._callObjectLoader.cancel(),t._callObjectLoader.load(t._callFrameId,t.properties.dailyConfig&&t.properties.dailyConfig.avoidEval,(function(n){t._updateCallState(Br),n&&t.emit(Ei,{action:Ei}),e()}),(function(e,r){if(t.emit(ki,{action:ki,errorMsg:e}),!r){t._updateCallState($r),t.resetMeetingDependentVars();var i={action:wo,errorMsg:e,error:{type:"connection-error",msg:"Failed to load call object bundle.",details:{on:"load",sourceError:new Error(e),bundleUrl:fe()}}};t._maybeSendToSentry(i),t.emit(wo,i),n(e)}}))})):(this._iframe.src=pe(this.assembleMeetingUrl()),new Promise((function(e,n){t._loadedCallback=function(r){if(t._callState!==$r){for(var i in t._updateCallState(Br),(t.properties.cssFile||t.properties.cssText)&&t.loadCss(t.properties),t._inputEventsOn)t.sendMessageToCallMachine({action:Mo,on:i});e()}else n(r)}})))}})),function(e){return T.apply(this,arguments)})},{key:"join",value:(C=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=!1;if(this.needsLoad()){this.updateIsPreparingToJoin(!0);try{yield this.load(t)}catch(e){return this.updateIsPreparingToJoin(!1),Promise.reject(e)}}else{if(n=!(!this.properties.cssFile&&!this.properties.cssText),this._didPreAuth){if(t.url&&t.url!==this.properties.url)return console.error("url in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject();if(t.token&&t.token!==this.properties.token)return console.error("token in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject()}if(t.url&&!this._callObjectMode&&t.url&&t.url!==this.properties.url)return console.error("url in join() is different than the one used in load() (".concat(this.properties.url," -> ").concat(t.url,")")),this.updateIsPreparingToJoin(!1),Promise.reject();this.validateProperties(t),this.properties=Zh(Zh({},this.properties),t)}if(void 0!==t.showLocalVideo&&(this._callObjectMode?console.error("showLocalVideo is not available in callObject mode"):this._showLocalVideo=!!t.showLocalVideo),void 0!==t.showParticipantsBar&&(this._callObjectMode?console.error("showParticipantsBar is not available in callObject mode"):this._showParticipantsBar=!!t.showParticipantsBar),this._callState===Ur||this._callState===Fr)return console.warn("already joined meeting, call leave() before joining again"),void this.updateIsPreparingToJoin(!1);this._updateCallState(Fr,!1);try{this.emit(Ti,{action:Ti})}catch(e){console.log("could not emit 'joining-meeting'",e)}return this._preloadCache.inputSettings||(this._preloadCache.inputSettings={}),t.inputSettings&&t.inputSettings.audio&&(this._preloadCache.inputSettings.audio=t.inputSettings.audio),t.inputSettings&&t.inputSettings.video&&(this._preloadCache.inputSettings.video=t.inputSettings.video),this.sendMessageToCallMachine({action:"join-meeting",properties:gp(this.properties),preloadCache:gp(this._preloadCache)}),new Promise((function(t,r){e._joinedCallback=function(i,o){if(e._callState!==$r){if(e._updateCallState(Ur),i)for(var a in i)e._callObjectMode&&(Ah(i[a]),Dh(i[a]),Nh(i[a],e._participants[a])),e._participants[a]=Zh({},i[a]),e.toggleParticipantAudioBasedOnNativeAudioFocus();n&&e.loadCss(e.properties),t(i)}else r(o)}}))})),function(){return C.apply(this,arguments)})},{key:"leave",value:(M=n((function*(){var e=this;return new Promise((function(t){if(e._callState===Vr||e._callState===$r)t();else if(e._callObjectLoader&&!e._callObjectLoader.loaded){e._callObjectLoader.cancel(),e._updateCallState(Vr),e.resetMeetingDependentVars();try{e.emit(Vr,{action:Vr})}catch(e){console.log("could not emit 'left-meeting'",e)}t()}else e._resolveLeave=t,e.sendMessageToCallMachine({action:"leave-meeting"})}))})),function(){return M.apply(this,arguments)})},{key:"startScreenShare",value:(E=n((function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._dailyMainExecuted)if(t.screenVideoSendSettings&&this._validateVideoSendSettings("screenVideo",t.screenVideoSendSettings),t.mediaStream&&(this._preloadCache.screenMediaStream=t.mediaStream,t.mediaStream=Oo),"undefined"!=typeof DailyNativeUtils&&void 0!==DailyNativeUtils.isIOS&&DailyNativeUtils.isIOS){var n=this.nativeUtils();if(yield n.isScreenBeingCaptured())return void this.emit(bo,{action:bo,type:"screen-share-error",errorMsg:"Could not start the screen sharing. The screen is already been captured!"});n.setSystemScreenCaptureStartCallback((function(){n.setSystemScreenCaptureStartCallback(null),e.sendMessageToCallMachine({action:Eo,captureOptions:t})})),n.presentSystemScreenCapturePrompt()}else this.sendMessageToCallMachine({action:Eo,captureOptions:t})})),function(){return E.apply(this,arguments)})},{key:"stopScreenShare",value:function(){this._dailyMainExecuted&&this.sendMessageToCallMachine({action:"local-screen-stop"})}},{key:"startRecording",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(Zh({action:"local-recording-start"},e))}},{key:"updateRecording",value:function(e){var t=e.layout,n=void 0===t?{preset:"default"}:t,r=e.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-recording",layout:n,instanceId:r})}},{key:"stopRecording",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(Zh({action:"local-recording-stop"},e))}},{key:"startLiveStreaming",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(Zh({action:"daily-method-start-live-streaming"},e))}},{key:"updateLiveStreaming",value:function(e){var t=e.layout,n=void 0===t?{preset:"default"}:t,r=e.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-live-streaming",layout:n,instanceId:r})}},{key:"addLiveStreamingEndpoints",value:function(e){var t=e.endpoints,n=e.instanceId;this.sendMessageToCallMachine({action:Co,endpointsOp:Lo,endpoints:t,instanceId:n})}},{key:"removeLiveStreamingEndpoints",value:function(e){var t=e.endpoints,n=e.instanceId;this.sendMessageToCallMachine({action:Co,endpointsOp:No,endpoints:t,instanceId:n})}},{key:"stopLiveStreaming",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(Zh({action:"daily-method-stop-live-streaming"},e))}},{key:"validateDailyConfig",value:function(e){e.camSimulcastEncodings&&(console.warn("camSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide camera simulcast settings."),this.validateSimulcastEncodings(e.camSimulcastEncodings)),e.screenSimulcastEncodings&&console.warn("screenSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide screen simulcast settings.")}},{key:"validateSimulcastEncodings",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e){if(!(e instanceof Array))throw new Error("encodings must be an Array");if(!Dp(e.length,1,3))throw new Error("encodings must be an Array with between 1 to ".concat(3," layers"));for(var r=0;r<e.length;r++){var i=e[r];for(var o in this._validateEncodingLayerHasValidProperties(i),i){if(!cp.includes(o))throw new Error("Invalid key ".concat(o,", valid keys are:")+Object.values(cp));if("number"!=typeof i[o])throw new Error("".concat(o," must be a number"));if(t){var a=t[o],s=a.min,c=a.max;if(!Dp(i[o],s,c))throw new Error("".concat(o," value not in range. valid range: ").concat(s," to ").concat(c))}}if(n&&!i.hasOwnProperty("maxBitrate"))throw new Error("maxBitrate is not specified")}}}},{key:"startRemoteMediaPlayer",value:(k=n((function*(e){var t=this,r=e.url,i=e.settings,o=void 0===i?{state:xo.PLAY}:i;try{!function(e){if("string"!=typeof e)throw new Error('url parameter must be "string" type')}(r),Ap(o),function(e){for(var t in e)if(!up.includes(t))throw new Error("Invalid key ".concat(t,", valid keys are: ").concat(up));e.simulcastEncodings&&this.validateSimulcastEncodings(e.simulcastEncodings,sp,!0)}(o)}catch(e){throw console.error("invalid argument Error: ".concat(e)),console.error('startRemoteMediaPlayer arguments must be of the form: \n  { url: "playback url", \n  settings?: \n  {state: "play"|"pause", simulcastEncodings?: [{}] } }'),e}return new Promise(function(){var e=n((function*(e,n){t.sendMessageToCallMachine({action:"daily-method-start-remote-media-player",url:r,settings:o},(function(t){t.error?n({error:t.error,errorMsg:t.errorMsg}):e({session_id:t.session_id,remoteMediaPlayerState:{state:t.state,settings:t.settings}})}))}));return function(t,n){return e.apply(this,arguments)}}())})),function(e){return k.apply(this,arguments)})},{key:"stopRemoteMediaPlayer",value:(S=n((function*(e){var t=this;if("string"!=typeof e)throw new Error(" remotePlayerID must be of type string");return new Promise(function(){var r=n((function*(n,r){t.sendMessageToCallMachine({action:"daily-method-stop-remote-media-player",session_id:e},(function(e){e.error?r({error:e.error,errorMsg:e.errorMsg}):n()}))}));return function(e,t){return r.apply(this,arguments)}}())})),function(e){return S.apply(this,arguments)})},{key:"updateRemoteMediaPlayer",value:(w=n((function*(e){var t=this,r=e.session_id,i=e.settings;try{Ap(i)}catch(e){throw console.error("invalid argument Error: ".concat(e)),console.error('updateRemoteMediaPlayer arguments must be of the form: \n  session_id: "participant session", \n  { settings?: {state: "play"|"pause"} }'),e}return new Promise(function(){var e=n((function*(e,n){t.sendMessageToCallMachine({action:"daily-method-update-remote-media-player",session_id:r,settings:i},(function(t){t.error?n({error:t.error,errorMsg:t.errorMsg}):e({session_id:t.session_id,remoteMediaPlayerState:{state:t.state,settings:t.settings}})}))}));return function(t,n){return e.apply(this,arguments)}}())})),function(e){return w.apply(this,arguments)})},{key:"startTranscription",value:function(e){this.sendMessageToCallMachine(Zh({action:"daily-method-start-transcription"},e))}},{key:"stopTranscription",value:function(){this.sendMessageToCallMachine({action:"daily-method-stop-transcription"})}},{key:"getNetworkStats",value:function(){var e=this;if(this._callState!==Ur){return{stats:{latest:{}}}}return new Promise((function(t){e.sendMessageToCallMachine({action:"get-calc-stats"},(function(n){t(Zh({stats:n.stats},e._network))}))}))}},{key:"testWebsocketConnectivity",value:(b=n((function*(){var e=this;if(this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}return new Promise((function(t,n){e.sendMessageToCallMachine({action:"test-websocket-connectivity"},(function(e){e.error?n(e.error):t(e.results)}))}))})),function(){return b.apply(this,arguments)})},{key:"abortTestWebsocketConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-websocket-connectivity"})}},{key:"_validateVideoTrackForNetworkTests",value:function(e){return e?e instanceof MediaStreamTrack?!!Hh(e,{isLocalScreenVideo:!1})||(console.error("Video track is not playable. This test needs a live video track."),!1):(console.error("Video track needs to be of type `MediaStreamTrack`."),!1):(console.error("Missing video track. You must provide a video track in order to run this test."),!1)}},{key:"testConnectionQuality",value:(_=n((function*(e){var t=this;if(this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}var n=e.videoTrack,r=e.duration;if(!this._validateVideoTrackForNetworkTests(n))throw new Error("Video track error");return this._preloadCache.videoTrackForConnectionQualityTest=n,new Promise((function(e,n){t.sendMessageToCallMachine({action:"test-connection-quality",duration:r},(function(t){t.error?n(t.error):e(t.results)}))}))})),function(e){return _.apply(this,arguments)})},{key:"stopTestConnectionQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-connection-quality"})}},{key:"testNetworkConnectivity",value:(m=n((function*(e){var t=this;if(this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}if(!this._validateVideoTrackForNetworkTests(e))throw new Error("Video track error");return this._preloadCache.videoTrackForNetworkConnectivityTest=e,new Promise((function(e,n){t.sendMessageToCallMachine({action:"test-network-connectivity"},(function(t){t.error?n(t.error):e(t.results)}))}))})),function(e){return m.apply(this,arguments)})},{key:"abortTestNetworkConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-network-connectivity"})}},{key:"getCpuLoadStats",value:function(){var e=this;return new Promise((function(t,n){if(e._callState===Ur){e.sendMessageToCallMachine({action:"get-cpu-load-stats"},(function(e){t(e.cpuStats)}))}else t({cpuLoadState:void 0,cpuLoadStateReason:void 0,stats:{}})}))}},{key:"_validateEncodingLayerHasValidProperties",value:function(e){var t;if(!((null===(t=Object.keys(e))||void 0===t?void 0:t.length)>0))throw new Error("Empty encoding is not allowed. At least one of these valid keys should be specified:"+Object.values(cp))}},{key:"_validateVideoSendSettings",value:function(e,t){var n="screenVideo"===e?["default-screen-video","detail-optimized","motion-optimized","motion-and-detail-balanced"]:["default-video","bandwidth-optimized","bandwidth-and-quality-balanced","quality-optimized"],r="Video send settings should be either an object or one of the supported presets: ".concat(n.join());if("string"==typeof t){if(!n.includes(t))throw new Error(r)}else{if("object"!==i(t))throw new Error(r);if(!t.maxQuality&&!t.encodings)throw new Error("Video send settings must contain at least maxQuality or encodings attribute");if(t.maxQuality&&-1===["low","medium","high"].indexOf(t.maxQuality))throw new Error("maxQuality must be either low, medium or high");if(t.encodings){var o=!1;switch(Object.keys(t.encodings).length){case 1:o=!t.encodings.low;break;case 2:o=!t.encodings.low||!t.encodings.medium;break;case 3:o=!t.encodings.low||!t.encodings.medium||!t.encodings.high;break;default:o=!0}if(o)throw new Error("Encodings must be defined as: low, low and medium, or low, medium and high.");t.encodings.low&&this._validateEncodingLayerHasValidProperties(t.encodings.low),t.encodings.medium&&this._validateEncodingLayerHasValidProperties(t.encodings.medium),t.encodings.high&&this._validateEncodingLayerHasValidProperties(t.encodings.high)}}}},{key:"validateUpdateSendSettings",value:function(e){var t=this;if(!e||0===Object.keys(e).length)throw new Error("Send settings must contain at least information for one track!");Object.entries(e).forEach((function(e){var n=v(e,2),r=n[0],i=n[1];t._validateVideoSendSettings(r,i)}))}},{key:"updateSendSettings",value:function(e){var t=this;return this.validateUpdateSendSettings(e),this.needsLoad()?(this._preloadCache.sendSettings=e,{sendSettings:this._preloadCache.sendSettings}):new Promise((function(n,r){t.sendMessageToCallMachine({action:"update-send-settings",sendSettings:e},(function(e){e.error?r(e.error):n(e.sendSettings)}))}))}},{key:"getSendSettings",value:function(){return this._sendSettings||this._preloadCache.sendSettings}},{key:"getLocalAudioLevel",value:function(){return this._localAudioLevel}},{key:"getRemoteParticipantsAudioLevel",value:function(){return this._remoteParticipantsAudioLevel}},{key:"getActiveSpeaker",value:function(){return bp(),this._activeSpeaker}},{key:"setActiveSpeakerMode",value:function(e){return bp(),this.sendMessageToCallMachine({action:"set-active-speaker-mode",enabled:e}),this}},{key:"activeSpeakerMode",value:function(){return bp(),this._activeSpeakerMode}},{key:"subscribeToTracksAutomatically",value:function(){return this._preloadCache.subscribeToTracksAutomatically}},{key:"setSubscribeToTracksAutomatically",value:function(e){return mp(this._callState,"setSubscribeToTracksAutomatically()","Use the subscribeToTracksAutomatically configuration property."),this._preloadCache.subscribeToTracksAutomatically=e,this.sendMessageToCallMachine({action:"daily-method-subscribe-to-tracks-automatically",enabled:e}),this}},{key:"enumerateDevices",value:(g=n((function*(){var e=this;return this._callObjectMode?{devices:(yield navigator.mediaDevices.enumerateDevices()).map((function(e){return JSON.parse(JSON.stringify(e))}))}:new Promise((function(t){e.sendMessageToCallMachine({action:"enumerate-devices"},(function(e){t({devices:e.devices})}))}))})),function(){return g.apply(this,arguments)})},{key:"sendAppMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"*";if(mp(this._callState,"sendAppMessage()"),JSON.stringify(e).length>4096)throw new Error("Message data too large. Max size is 4096");return this.sendMessageToCallMachine({action:"app-msg",data:e,to:t}),this}},{key:"addFakeParticipant",value:function(e){return bp(),mp(this._callState,"addFakeParticipant()"),this.sendMessageToCallMachine(Zh({action:"add-fake-participant"},e)),this}},{key:"setShowNamesMode",value:function(e){return _p(this._callObjectMode,"setShowNamesMode()"),bp(),e&&"always"!==e&&"never"!==e?(console.error('setShowNamesMode argument should be "always", "never", or false'),this):(this.sendMessageToCallMachine({action:"set-show-names",mode:e}),this)}},{key:"setShowLocalVideo",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return _p(this._callObjectMode,"setShowLocalVideo()"),bp(),mp(this._callState,"setShowLocalVideo()"),"boolean"!=typeof e?(console.error("setShowLocalVideo only accepts a boolean value"),this):(this.sendMessageToCallMachine({action:"set-show-local-video",show:e}),this._showLocalVideo=e,this)}},{key:"showLocalVideo",value:function(){return _p(this._callObjectMode,"showLocalVideo()"),bp(),this._showLocalVideo}},{key:"setShowParticipantsBar",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return _p(this._callObjectMode,"setShowParticipantsBar()"),bp(),mp(this._callState,"setShowParticipantsBar()"),"boolean"!=typeof e?(console.error("setShowParticipantsBar only accepts a boolean value"),this):(this.sendMessageToCallMachine({action:"set-show-participants-bar",show:e}),this._showParticipantsBar=e,this)}},{key:"showParticipantsBar",value:function(){return _p(this._callObjectMode,"showParticipantsBar()"),bp(),this._showParticipantsBar}},{key:"customIntegrations",value:function(){return bp(),_p(this._callObjectMode,"customIntegrations()"),this._customIntegrations}},{key:"setCustomIntegrations",value:function(e){return bp(),_p(this._callObjectMode,"setCustomIntegrations()"),mp(this._callState,"setCustomIntegrations()"),Pp(e)?(this.sendMessageToCallMachine({action:"set-custom-integrations",integrations:e}),this._customIntegrations=e,this):this}},{key:"startCustomIntegrations",value:function(e){var t=this;if(bp(),_p(this._callObjectMode,"startCustomIntegrations()"),mp(this._callState,"startCustomIntegrations()"),Array.isArray(e)&&e.some((function(e){return"string"!=typeof e}))||!Array.isArray(e)&&"string"!=typeof e)return console.error("startCustomIntegrations() only accepts string | string[]"),this;var n="string"==typeof e?[e]:e,r=n.filter((function(e){return!(e in t._customIntegrations)}));return r.length?(console.error("Can't find custom integration(s): \"".concat(r.join(", "),'"')),this):(this.sendMessageToCallMachine({action:"start-custom-integrations",ids:n}),this)}},{key:"stopCustomIntegrations",value:function(e){var t=this;if(bp(),_p(this._callObjectMode,"stopCustomIntegrations()"),mp(this._callState,"stopCustomIntegrations()"),Array.isArray(e)&&e.some((function(e){return"string"!=typeof e}))||!Array.isArray(e)&&"string"!=typeof e)return console.error("stopCustomIntegrations() only accepts string | string[]"),this;var n="string"==typeof e?[e]:e,r=n.filter((function(e){return!(e in t._customIntegrations)}));return r.length?(console.error("Can't find custom integration(s): \"".concat(r.join(", "),'"')),this):(this.sendMessageToCallMachine({action:"stop-custom-integrations",ids:n}),this)}},{key:"customTrayButtons",value:function(){return _p(this._callObjectMode,"customTrayButtons()"),bp(),this._customTrayButtons}},{key:"updateCustomTrayButtons",value:function(e){return _p(this._callObjectMode,"updateCustomTrayButtons()"),bp(),mp(this._callState,"updateCustomTrayButtons()"),jp(e)?(this.sendMessageToCallMachine({action:"update-custom-tray-buttons",btns:e}),this._customTrayButtons=e,this):(console.error("updateCustomTrayButtons only accepts a dictionary of the type ".concat(JSON.stringify(dp))),this)}},{key:"theme",value:function(){return _p(this._callObjectMode,"theme()"),this.properties.theme}},{key:"setTheme",value:function(e){var t=this;return _p(this._callObjectMode,"setTheme()"),new Promise((function(n,r){try{t.validateProperties({theme:e}),t.properties.theme=Zh({},e),t.sendMessageToCallMachine({action:"set-theme",theme:t.properties.theme});try{t.emit(wi,{action:wi,theme:t.properties.theme})}catch(e){console.log("could not emit 'theme-updated'",e)}n(t.properties.theme)}catch(e){r(e)}}))}},{key:"detectAllFaces",value:function(){var e=this;return bp(),new Promise((function(t){e.sendMessageToCallMachine({action:"detect-all-faces"},(function(e){delete e.action,delete e.callbackStamp,t(e)}))}))}},{key:"requestFullscreen",value:(f=n((function*(){if(bp(),this._iframe&&!document.fullscreenElement&&Uo())try{(yield this._iframe.requestFullscreen)?this._iframe.requestFullscreen():this._iframe.webkitRequestFullscreen()}catch(e){console.log("could not make video call fullscreen",e)}})),function(){return f.apply(this,arguments)})},{key:"exitFullscreen",value:function(){bp(),document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen()}},{key:"getSidebarView",value:(h=n((function*(){var e=this;return this._callObjectMode?(console.error("getSidebarView is not available in callObject mode"),Promise.resolve(null)):new Promise((function(t){e.sendMessageToCallMachine({action:"get-sidebar-view"},(function(e){t(e.view)}))}))})),function(){return h.apply(this,arguments)})},{key:"setSidebarView",value:function(e){return this._callObjectMode?(console.error("setSidebarView is not available in callObject mode"),this):(this.sendMessageToCallMachine({action:"set-sidebar-view",view:e}),this)}},{key:"room",value:(d=n((function*(){var e=this,t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).includeRoomConfigDefaults,n=void 0===t||t;return this._accessState.access===Hr||this.needsLoad()?this.properties.url?{roomUrlPendingJoin:this.properties.url}:null:new Promise((function(t){e.sendMessageToCallMachine({action:"lib-room-info",includeRoomConfigDefaults:n},(function(e){delete e.action,delete e.callbackStamp,t(e)}))}))})),function(){return d.apply(this,arguments)})},{key:"geo",value:(u=n((function*(){return new Promise(function(){var e=n((function*(e){try{var t=yield fetch("https://gs.daily.co/_ks_/x-swsl/:");e({current:(yield t.json()).geo})}catch(t){console.error("geo lookup failed",t),e({current:""})}}));return function(t){return e.apply(this,arguments)}}())})),function(){return u.apply(this,arguments)})},{key:"setNetworkTopology",value:(a=n((function*(e){var t=this;return bp(),new Promise(function(){var r=n((function*(n,r){t.sendMessageToCallMachine({action:"set-network-topology",opts:e},(function(e){e.error?r({error:e.error}):n({workerId:e.workerId})}))}));return function(e,t){return r.apply(this,arguments)}}())})),function(e){return a.apply(this,arguments)})},{key:"getNetworkTopology",value:(o=n((function*(){var e=this;return new Promise(function(){var t=n((function*(t,n){e.sendMessageToCallMachine({action:"get-network-topology"},(function(e){e.error?n({error:e.error}):t({topology:e.topology})}))}));return function(e,n){return t.apply(this,arguments)}}())})),function(){return o.apply(this,arguments)})},{key:"setPlayNewParticipantSound",value:function(e){if(bp(),"number"!=typeof e&&!0!==e&&!1!==e)throw new Error("argument to setShouldPlayNewParticipantSound should be true, false, or a number, but is ".concat(e));this.sendMessageToCallMachine({action:"daily-method-set-play-ding",arg:e})}},{key:"on",value:function(e,t){return this._inputEventsOn[e]={},this.sendMessageToCallMachine({action:Mo,on:e}),y.prototype.on.call(this,e,t)}},{key:"once",value:function(e,t){return this._inputEventsOn[e]={},this.sendMessageToCallMachine({action:Mo,on:e}),y.prototype.once.call(this,e,t)}},{key:"off",value:function(e,t){return delete this._inputEventsOn[e],this.isDestroyed()||this.sendMessageToCallMachine({action:Mo,off:e}),y.prototype.off.call(this,e,t)}},{key:"validateProperties",value:function(e){for(var t in e){if(!pp[t])throw new Error("unrecognized property '".concat(t,"'"));if(pp[t].validate&&!pp[t].validate(e[t],this))throw new Error("property '".concat(t,"': ").concat(pp[t].help))}}},{key:"assembleMeetingUrl",value:function(){var e,t,n=Zh(Zh({},this.properties),{},{emb:this._callFrameId,embHref:encodeURIComponent(window.location.href),proxy:null!==(e=window._dailyConfig)&&void 0!==e&&e.proxyUrl?encodeURIComponent(null===(t=window._dailyConfig)||void 0===t?void 0:t.proxyUrl):void 0}),r=n.url.match(/\?/)?"&":"?";return n.url+r+Object.keys(pp).filter((function(e){return pp[e].queryString&&void 0!==n[e]})).map((function(e){return"".concat(pp[e].queryString,"=").concat(n[e])})).join("&")}},{key:"needsLoad",value:function(){return[Ir,Rr,Vr,$r].includes(this._callState)}},{key:"sendMessageToCallMachine",value:function(e,t){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw new Error("Use after destroy");this._messageChannel.sendMessageToCallMachine(e,t,this._iframe,this._callFrameId)}},{key:"forwardPackagedMessageToCallMachine",value:function(e){this._messageChannel.forwardPackagedMessageToCallMachine(e,this._iframe,this._callFrameId)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e){return this._messageChannel.addListenerForPackagedMessagesFromCallMachine(e,this._callFrameId)}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){this._messageChannel.removeListenerForPackagedMessagesFromCallMachine(e)}},{key:"handleMessageFromCallMachine",value:function(t){switch(t.action){case _i:this.sendMessageToCallMachine(Zh({action:bi},this.properties));break;case"daily-main-executed":this._dailyMainExecuted=!0;case Ei:this._loadedCallback&&(this._loadedCallback(),this._loadedCallback=null);try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}break;case Oi:this._joinedCallback&&(this._joinedCallback(t.participants),this._joinedCallback=null);try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}break;case xi:case Ai:if(this._callState===Vr)return;if(t.participant&&t.participant.session_id){var n=t.participant.local?"local":t.participant.session_id;this._callObjectMode&&(Ah(t.participant),Dh(t.participant),Nh(t.participant,this._participants[n]));try{this.maybeParticipantTracksStopped(this._participants[n],t.participant),this.maybeParticipantTracksStarted(this._participants[n],t.participant),this.maybeEventRecordingStopped(this._participants[n],t.participant),this.maybeEventRecordingStarted(this._participants[n],t.participant)}catch(e){console.error("track events error",e)}if(!this.compareEqualForParticipantUpdateEvent(t.participant,this._participants[n])){this._participants[n]=Zh({},t.participant),this.toggleParticipantAudioBasedOnNativeAudioFocus();try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}}}break;case Di:if(t.participant&&t.participant.session_id){var r=this._participants[t.participant.session_id];r&&this.maybeParticipantTracksStopped(r,null),delete this._participants[t.participant.session_id];try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}}break;case Li:if(!Q(this._participantCounts,t.participantCounts)){this._participantCounts=t.participantCounts;try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}}break;case Ni:var i={access:t.access};if(t.awaitingAccess&&(i.awaitingAccess=t.awaitingAccess),!Q(this._accessState,i)){this._accessState=i;try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}}break;case Ii:if(t.meetingSession){this._meetingSessionSummary=t.meetingSession;try{delete t.callFrameId,this.emit(t.action,t);var o=Zh(Zh({},t),{},{action:"meeting-session-updated"});this.emit(o.action,o)}catch(e){console.log("could not emit",t,e)}}break;case wo:var a,s;this._iframe&&!t.preserveIframe&&(this._iframe.src=""),this._updateCallState($r),this.resetMeetingDependentVars(),this._loadedCallback&&(this._loadedCallback(t.errorMsg),this._loadedCallback=null),t.preserveIframe;var c=e(t,Qh);null!=c&&null!==(a=c.error)&&void 0!==a&&null!==(s=a.details)&&void 0!==s&&s.sourceError&&(c.error.details.sourceError=JSON.parse(c.error.details.sourceError)),this._maybeSendToSentry(t),this._joinedCallback&&(this._joinedCallback(null,c),this._joinedCallback=null);try{this.emit(t.action,c)}catch(e){console.log("could not emit",t,e)}break;case ji:this._callState!==$r&&this._updateCallState(Vr),this.resetMeetingDependentVars(),this._resolveLeave&&(this._resolveLeave(),this._resolveLeave=null);try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}break;case"selected-devices-updated":if(t.devices)try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}break;case so:var u=t.threshold,l=t.quality;if(u!==this._network.threshold||l!==this._network.quality){this._network.quality=l,this._network.threshold=u;try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}}break;case uo:if(t&&t.cpuLoadState)try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}break;case oo:var d=t.activeSpeaker;if(this._activeSpeaker.peerId!==d.peerId){this._activeSpeaker.peerId=d.peerId;try{this.emit(t.action,{action:t.action,activeSpeaker:this._activeSpeaker})}catch(e){console.log("could not emit",t,e)}}break;case"show-local-video-changed":if(this._callObjectMode)return;var h=t.show;this._showLocalVideo=h;try{this.emit(t.action,{action:t.action,show:h})}catch(e){console.log("could not emit",t,e)}break;case ao:var p=t.enabled;if(this._activeSpeakerMode!==p){this._activeSpeakerMode=p;try{this.emit(t.action,{action:t.action,enabled:this._activeSpeakerMode})}catch(e){console.log("could not emit",t,e)}}break;case Fi:case Ui:case Vi:this._waitingParticipants=t.allWaitingParticipants;try{this.emit(t.action,{action:t.action,participant:t.participant})}catch(e){console.log("could not emit",t,e)}break;case yo:if(!Q(this._receiveSettings,t.receiveSettings)){this._receiveSettings=t.receiveSettings;try{this.emit(t.action,{action:t.action,receiveSettings:t.receiveSettings})}catch(e){console.log("could not emit",t,e)}}break;case _o:if(!Q(this._inputSettings,t.inputSettings)){var f=this._getInputSettings();if(this._inputSettings=t.inputSettings,this._preloadCache.inputSettings={},!Q(f,this._getInputSettings()))try{this.emit(t.action,{action:t.action,inputSettings:this._getInputSettings()})}catch(e){console.log("could not emit",t,e)}}break;case"send-settings-updated":if(!Q(this._sendSettings,t.sendSettings)){this._sendSettings=t.sendSettings,this._preloadCache.sendSettings=null;try{this.emit(t.action,{action:t.action,sendSettings:t.sendSettings})}catch(e){console.log("could not emit",t,e)}}break;case"local-audio-level":this._localAudioLevel=t.audioLevel,this.emitDailyJSEvent(t);break;case"remote-participants-audio-level":this._remoteParticipantsAudioLevel=t.participantsAudioLevel,this.emitDailyJSEvent(t);break;case eo:var v=t.session_id;this._rmpPlayerState[v]=t.playerState,this.emitDailyJSEvent(t);break;case no:delete this._rmpPlayerState[t.session_id],this.emitDailyJSEvent(t);break;case to:var g=t.session_id,m=this._rmpPlayerState[g];m&&this.compareEqualForRMPUpdateEvent(m,t.remoteMediaPlayerState)||(this._rmpPlayerState[g]=t.remoteMediaPlayerState,this.emitDailyJSEvent(t));break;case"custom-button-click":case"sidebar-view-changed":this.emitDailyJSEvent(t);break;case Ri:var y=this._meetingSessionState.topology!==(t.meetingSessionState&&t.meetingSessionState.topology);this._meetingSessionState=Lp(t.meetingSessionState,this._callObjectMode),(this._callObjectMode||y)&&this.emitDailyJSEvent(t);break;case Ji:case Wi:case Hi:case Ki:case Qi:case qi:case Yi:case zi:case Mi:case Ci:case Zi:case ro:case io:case co:case Xi:case po:case fo:case vo:case go:case bo:case mo:try{this.emit(t.action,t)}catch(e){console.log("could not emit",t,e)}break;case"request-fullscreen":this.requestFullscreen();break;case"request-exit-fullscreen":this.exitFullscreen()}}},{key:"maybeEventRecordingStopped",value:function(e,t){var n="record";if(e&&!t.local&&!1===t[n]&&e[n]!==t[n])try{this.emit(Wi,{action:Wi})}catch(e){console.log("could not emit",e)}}},{key:"maybeEventRecordingStarted",value:function(e,t){var n="record";if(e&&!t.local&&!0===t[n]&&e[n]!==t[n])try{this.emit(Ji,{action:Ji})}catch(e){console.log("could not emit",e)}}},{key:"maybeEventTrackStopped",value:function(e,t,n,r){if(e&&("ended"===e.readyState||!t||e.id!==t.id))try{this.emit(Gi,{action:Gi,track:e,participant:n,type:r})}catch(e){console.log("maybeEventTrackStopped: could not emit",e)}}},{key:"maybeEventTrackStarted",value:function(e,t,n,r){if(t&&(!e||"ended"===e.readyState||t.id!==e.id))try{this.emit($i,{action:$i,track:t,participant:n,type:r})}catch(e){console.log("maybeEventTrackStarted: could not emit",e)}}},{key:"maybeParticipantTracksStopped",value:function(e,t){if(e)for(var n in e.tracks)this.maybeEventTrackStopped(e.tracks[n].track,t&&t.tracks[n]?t.tracks[n].track:null,t,n)}},{key:"maybeParticipantTracksStarted",value:function(e,t){if(t)for(var n in t.tracks)this.maybeEventTrackStarted(e&&e.tracks[n]?e.tracks[n].track:null,t.tracks[n].track,t,n)}},{key:"compareEqualForRMPUpdateEvent",value:function(e,t){var n,r;return e.state===t.state&&(null===(n=e.settings)||void 0===n?void 0:n.volume)===(null===(r=t.settings)||void 0===r?void 0:r.volume)}},{key:"emitDailyJSEvent",value:function(e){try{this.emit(e.action,e)}catch(t){console.log("could not emit",e,t)}}},{key:"compareEqualForParticipantUpdateEvent",value:function(e,t){return!!Q(e,t)&&((!e.videoTrack||!t.videoTrack||e.videoTrack.id===t.videoTrack.id&&e.videoTrack.muted===t.videoTrack.muted&&e.videoTrack.enabled===t.videoTrack.enabled)&&(!e.audioTrack||!t.audioTrack||e.audioTrack.id===t.audioTrack.id&&e.audioTrack.muted===t.audioTrack.muted&&e.audioTrack.enabled===t.audioTrack.enabled))}},{key:"nativeUtils",value:function(){return Ro()?"undefined"==typeof DailyNativeUtils?(console.warn("in React Native, DailyNativeUtils is expected to be available"),null):DailyNativeUtils:null}},{key:"updateIsPreparingToJoin",value:function(e){this._updateCallState(this._callState,e)}},{key:"_updateCallState",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._isPreparingToJoin;if(e!==this._callState||t!==this._isPreparingToJoin){var n=this._callState,r=this._isPreparingToJoin;this._callState=e,this._isPreparingToJoin=t;var i=this._isCallPendingOrOngoing(n,r),o=this._isCallPendingOrOngoing(this._callState,this._isPreparingToJoin);i!==o&&(this.updateKeepDeviceAwake(o),this.updateDeviceAudioMode(o),this.updateShowAndroidOngoingMeetingNotification(o),this.updateNoOpRecordingEnsuringBackgroundContinuity(o))}}},{key:"resetMeetingDependentVars",value:function(){this._participants={},this._participantCounts=ap,this._waitingParticipants={},this._activeSpeaker={},this._activeSpeakerMode=!1,this._didPreAuth=!1,this._accessState={access:Hr},this._finalSummaryOfPrevSession=this._meetingSessionSummary,this._meetingSessionSummary={},this._meetingSessionState=Lp(op,this._callObjectMode),this._receiveSettings={},this._inputSettings=void 0,this._sendSettings={},this._localAudioLevel=0,this._remoteParticipantsAudioLevel={},this._dailyMainExecuted=!1,this._preloadCache}},{key:"updateKeepDeviceAwake",value:function(e){Ro()&&this.nativeUtils().setKeepDeviceAwake(e,this._callFrameId)}},{key:"updateDeviceAudioMode",value:function(e){if(Ro()&&!this.disableReactNativeAutoDeviceManagement("audio")){var t=e?this._nativeInCallAudioMode:"idle";this.nativeUtils().setAudioMode(t)}}},{key:"updateShowAndroidOngoingMeetingNotification",value:function(e){if(Ro()&&this.nativeUtils().setShowOngoingMeetingNotification){var t,n,r,i;if(this.properties.reactNativeConfig&&this.properties.reactNativeConfig.androidInCallNotification){var o=this.properties.reactNativeConfig.androidInCallNotification;t=o.title,n=o.subtitle,r=o.iconName,i=o.disableForCustomOverride}i&&(e=!1),this.nativeUtils().setShowOngoingMeetingNotification(e,t,n,r,this._callFrameId)}}},{key:"updateNoOpRecordingEnsuringBackgroundContinuity",value:function(e){Ro()&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity(e)}},{key:"_isCallPendingOrOngoing",value:function(e,t){return[Fr,Ur].includes(e)||t}},{key:"toggleParticipantAudioBasedOnNativeAudioFocus",value:function(){if(Ro()){var e=window.store.getState();for(var t in e.streams){var n=e.streams[t];n&&n.pendingTrack&&"audio"===n.pendingTrack.kind&&(n.pendingTrack.enabled=this._hasNativeAudioFocus)}}}},{key:"disableReactNativeAutoDeviceManagement",value:function(e){return this.properties.reactNativeConfig&&this.properties.reactNativeConfig.disableAutoDeviceManagement&&this.properties.reactNativeConfig.disableAutoDeviceManagement[e]}},{key:"absoluteUrl",value:function(e){if(void 0!==e){var t=document.createElement("a");return t.href=e,t.href}}},{key:"sayHello",value:function(){var e="hello, world.";return console.log(e),e}},{key:"_logUseAfterDestroy",value:function(){if(this.needsLoad()){if(Kh&&!Kh.needsLoad()){var e={action:To,level:"error",code:this.strictMode?9995:9997};Kh.sendMessageToCallMachine(e)}else if(!this.strictMode){console.error("You are are attempting to use a call instance that was previously destroyed, which is unsupported. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix this unsupported usage.")}}else{var t={action:To,level:"error",code:this.strictMode?9995:9997};this._messageChannel.sendMessageToCallMachine(t,null,this._iframe,this._callFrameId)}}},{key:"_logDuplicateInstanceAttempt",value:function(){if(Kh.needsLoad()){if(!this.strictMode){console.error("You are attempting to use multiple call instances simultaneously. This is unsupported and will result in unknown errors. Previous instances should be destroyed before creating new ones. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix these attempts.")}}else Kh.sendMessageToCallMachine({action:To,level:"error",code:this.strictMode?9990:9992})}},{key:"_maybeSendToSentry",value:function(e){var t,n,r,i,o,a;if(null!==(t=e.error)&&void 0!==t&&t.type){if(!["connection-error","end-of-life","no-room"].includes(e.error.type))return}var s=null!==(n=this.properties)&&void 0!==n&&n.url?new URL(this.properties.url):void 0,c="production";s&&s.host.includes(".staging.daily")&&(c="staging");var u,l,d,h,p,f=new dr({dsn:"https://<EMAIL>/168844",transport:pr,integrations:[new Nr.GlobalHandlers({onunhandledrejection:!1})],environment:c}),v=new yn(f,void 0,z.version());if(this.session_id&&v.setExtra("sessionId",this.session_id),this.properties){var g=Zh({},this.properties);g.userName=g.userName?"[Filtered]":void 0,g.userData=g.userData?"[Filtered]":void 0,g.token=g.token?"[Filtered]":void 0,v.setExtra("properties",g)}if(s){var m=s.searchParams.get("domain");if(!m){var y=s.host.match(/(.*?)\./);m=y&&y[1]||""}m&&v.setTag("domain",m)}e.error&&(v.setTag("fatalErrorType",e.error.type),v.setExtra("errorDetails",e.error.details),(null===(u=e.error.details)||void 0===u?void 0:u.uri)&&v.setTag("serverAddress",e.error.details.uri),(null===(l=e.error.details)||void 0===l?void 0:l.workerGroup)&&v.setTag("workerGroup",e.error.details.workerGroup),(null===(d=e.error.details)||void 0===d?void 0:d.geoGroup)&&v.setTag("geoGroup",e.error.details.geoGroup),(null===(h=e.error.details)||void 0===h?void 0:h.bundleUrl)&&v.setTag("bundleUrl",e.error.details.bundleUrl),(null===(p=e.error.details)||void 0===p?void 0:p.on)&&v.setTag("connectionAttempt",e.error.details.on));v.setTags({callMode:this._callObjectMode?Ro()?"reactNative":null!==(r=this.properties)&&void 0!==r&&null!==(i=r.dailyConfig)&&void 0!==i&&null!==(o=i.callMode)&&void 0!==o&&o.includes("prebuilt")?this.properties.dailyConfig.callMode:"custom":"prebuilt-frame",version:z.version()});var _=(null===(a=e.error)||void 0===a?void 0:a.msg)||e.errorMsg;v.run((function(e){e.captureException(new Error(_))}))}}],[{key:"supportedBrowser",value:function(){if(Ro())return{supported:!0,mobile:!0,name:"React Native",version:null,supportsScreenShare:!0,supportsSfu:!0,supportsVideoProcessing:!1,supportsAudioProcessing:!1};var e=le.getParser(Io());return{supported:!!Yo(),mobile:"mobile"===e.getPlatformType(),name:e.getBrowserName(),version:e.getBrowserVersion(),supportsFullscreen:!!Uo(),supportsScreenShare:!!Fo(),supportsSfu:!!Yo(),supportsVideoProcessing:$o(),supportsAudioProcessing:qo()}}},{key:"version",value:function(){return"0.55.1"}},{key:"createCallObject",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.layout="none",new z(null,e)}},{key:"wrap",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(bp(),!e||!e.contentWindow||"string"!=typeof e.src)throw new Error("DailyIframe::Wrap needs an iframe-like first argument");return t.layout||(t.customLayout?t.layout="custom-v1":t.layout="browser"),new z(e,t)}},{key:"createFrame",value:function(e,t){var n,r;bp(),e&&t?(n=e,r=t):e&&e.append?(n=e,r={}):(n=document.body,r=e||{});var i=r.iframeStyle;i||(i=n===document.body?{position:"fixed",border:"1px solid black",backgroundColor:"white",width:"375px",height:"450px",right:"1em",bottom:"1em"}:{border:0,width:"100%",height:"100%"});var o=document.createElement("iframe");window.navigator&&window.navigator.userAgent.match(/Chrome\/61\./)?o.allow="microphone, camera":o.allow="microphone; camera; autoplay; display-capture; screen-wake-lock",o.style.visibility="hidden",n.appendChild(o),o.style.visibility=null,Object.keys(i).forEach((function(e){return o.style[e]=i[e]})),r.layout||(r.customLayout?r.layout="custom-v1":r.layout="browser");try{return new z(o,r)}catch(e){throw n.removeChild(o),e}}},{key:"createTransparentFrame",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};bp();var t=document.createElement("iframe");return t.allow="microphone; camera; autoplay",t.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: 0;\n      pointer-events: none;\n    ",document.body.appendChild(t),e.layout||(e.layout="custom-v1"),z.wrap(t,e)}},{key:"getCallInstance",value:function(){return Kh}}]),z}();function gp(e){var t={};for(var n in e)e[n]instanceof MediaStreamTrack?t[n]=Oo:"dailyConfig"===n?(e[n].modifyLocalSdpHook&&(window._dailyConfig&&(window._dailyConfig.modifyLocalSdpHook=e[n].modifyLocalSdpHook),delete e[n].modifyLocalSdpHook),e[n].modifyRemoteSdpHook&&(window._dailyConfig&&(window._dailyConfig.modifyRemoteSdpHook=e[n].modifyRemoteSdpHook),delete e[n].modifyRemoteSdpHook),t[n]=e[n]):t[n]=e[n];return t}function mp(e){var t=arguments.length>2?arguments[2]:void 0;if(e!==Ur){var n="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," only supported after join.");throw t&&(n+=" ".concat(t)),console.error(n),new Error(n)}}function yp(e){if(!e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," is only supported on custom callObject instances");throw console.error(t),new Error(t)}}function _p(e){if(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," is only supported as part of Daily's Prebuilt");throw console.error(t),new Error(t)}}function bp(){if(Ro())throw new Error("This daily-js method is not currently supported in React Native")}function wp(){if(!Ro())throw new Error("This daily-js method is only supported in React Native")}function Sp(e){if(void 0===e)return!0;var t;if("string"==typeof e)t=e;else try{t=JSON.stringify(e),Q(JSON.parse(t),e)||console.warn("The userData provided will be modified when serialized.")}catch(e){throw Error("userData must be serializable to JSON: ".concat(e))}if(t.length>4096)throw Error("userData is too large (".concat(t.length," characters). Maximum size suppported is ").concat(4096,"."));return!0}function kp(e,t){for(var n=t.allowAllParticipantsKey,r=function(e){var t=["local"];return n||t.push("*"),e&&!t.includes(e)},i=function(e){return!!(void 0===e.layer||Number.isInteger(e.layer)&&e.layer>=0||"inherit"===e.layer)},o=function(e){return!!e&&(!(e.video&&!i(e.video))&&!(e.screenVideo&&!i(e.screenVideo)))},a=0,s=Object.entries(e);a<s.length;a++){var c=v(s[a],2),u=c[0],l=c[1];if(!r(u)||!o(l))return!1}return!0}function Ep(e){return"object"===i(e)&&(!!(e.video&&"object"===i(e.video)||e.audio&&"object"===i(e.audio))&&(!(e.video&&!function(e){var t=["type","config"];if(!e)return!1;if("object"!==i(e))return!1;if(!function(e){if("string"!=typeof e)return!1;if(!Object.values(jo).includes(e))return console.error("inputSettings video processor type invalid"),!1;return!0}(e.type))return!1;if(e.config){if("object"!==i(e.config))return!1;if(!function(e,t){var n=Object.keys(t);if(0===n.length)return!0;var r="invalid object in inputSettings -> video -> processor -> config";switch(e){case jo.BGBLUR:return n.length>1||"strength"!==n[0]?(console.error(r),!1):!("number"!=typeof t.strength||t.strength<=0||t.strength>1||isNaN(t.strength))||(console.error("".concat(r,"; expected: {0 < strength <= 1}, got: ").concat(t.strength)),!1);case jo.BGIMAGE:return!(void 0!==t.source&&!function(e){if("default"===e.source)return e.type="default",!0;if(ve(e.source))return e.type="url",!!function(e){var t=new URL(e),n=t.pathname;if("data:"===t.protocol)try{var r=n.substring(n.indexOf(":")+1,n.indexOf(";")).split("/")[1];return Do.includes(r)}catch(e){return console.error("failed to deduce blob content type",e),!1}var i=n.split(".").at(-1).toLowerCase().trim();return Do.includes(i)}(e.source)||(console.error("invalid image type; supported types: [".concat(Do.join(", "),"]")),!1);return t=e.source,n=Number(t),isNaN(n)||!Number.isInteger(n)||n<=0||n>Ao?(console.error("invalid image selection; must be an int, > 0, <= ".concat(Ao)),!1):(e.type="daily-preselect",!0);var t,n}(t));default:return!0}}(e.type,e.config))return!1}return Object.keys(e).filter((function(e){return!t.includes(e)})).forEach((function(t){console.warn("invalid key inputSettings -> video -> processor : ".concat(t)),delete e[t]})),!0}(e.video.processor))&&!(e.audio&&(n=e.audio.processor,r=["type"],!n||"object"!==i(n)||(Object.keys(n).filter((function(e){return!r.includes(e)})).forEach((function(e){console.warn("invalid key inputSettings -> audio -> processor : ".concat(e)),delete n[e]})),t=n.type,"string"!=typeof t||!Object.values(Po).includes(t)&&(console.error("inputSettings audio processor type invalid"),1))))));var t,n,r}function Mp(e){var t=[];e.video&&!$o()&&(delete e.video,t.push("video")),e.audio&&!qo()&&(delete e.audio,t.push("audio")),t.length>0&&console.error("Ignoring settings for browser- or platform-unsupported input processor(s): ".concat(t.join(", ")))}function Cp(){var e=Object.values(jo).join(" | "),t=Object.values(Po).join(" | ");return"inputSettings must be of the form: { video?: { processor: { type: [ ".concat(e," ], config?: {} } }, audio?: { processor: {type: [ ").concat(t," ] } } }")}function Tp(e){var t=e.allowAllParticipantsKey;return"receiveSettings must be of the form { [<remote participant id> | ".concat(Zr).concat(t?' | "'.concat(ei,'"'):"","]: ")+'{ [video: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]], [screenVideo: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]] }}}'}function Op(){return"customIntegrations should be an object of type ".concat(JSON.stringify(hp),".")}function jp(e){if(e&&"object"!==i(e)||Array.isArray(e))return console.error("customTrayButtons should be an Object of the type ".concat(JSON.stringify(dp),".")),!1;if(e)for(var t=0,n=Object.entries(e);t<n.length;t++)for(var r=v(n[t],1)[0],o=0,a=Object.entries(e[r]);o<a.length;o++){var s=v(a[o],2),c=s[0],u=s[1];if("iconPath"===c&&!ve(u))return console.error("customTrayButton ".concat(c," should be a url.")),!1;if("iconPathDarkMode"===c&&!ve(u))return console.error("customTrayButton ".concat(c," should be a url.")),!1;var l=dp.id[c];if(!l)return console.error("customTrayButton does not support key ".concat(c)),!1;if(i(u)!==l)return console.error("customTrayButton ".concat(c," should be a ").concat(l,".")),!1}return!0}function Pp(e){if(!e||e&&"object"!==i(e)||Array.isArray(e))return console.error(Op()),!1;for(var t=function(e){return"".concat(e," should be ").concat(hp.id[e])},n=function(e,t){return console.error("customIntegration ".concat(e,": ").concat(t))},r=0,o=Object.entries(e);r<o.length;r++){var a=v(o[r],1)[0];if(!("label"in e[a]))return n(a,"label is required"),!1;if(!("location"in e[a]))return n(a,"location is required"),!1;if(!("src"in e[a])&&!("srcdoc"in e[a]))return n(a,"src or srcdoc is required"),!1;for(var s=0,c=Object.entries(e[a]);s<c.length;s++){var u=v(c[s],2),l=u[0],d=u[1];switch(l){case"allow":case"csp":case"name":case"referrerPolicy":case"sandbox":if("string"!=typeof d)return n(a,t(l)),!1;break;case"iconURL":if(!ve(d))return n(a,"".concat(l," should be a url")),!1;break;case"src":if("srcdoc"in e[a])return n(a,"cannot have both src and srcdoc"),!1;if(!ve(d))return n(a,'src "'.concat(d,'" is not a valid URL')),!1;break;case"srcdoc":if("src"in e[a])return n(a,"cannot have both src and srcdoc"),!1;if("string"!=typeof d)return n(a,t(l)),!1;break;case"location":if(!["main","sidebar"].includes(d))return n(a,t(l)),!1;break;case"controlledBy":if("*"!==d&&"owners"!==d&&(!Array.isArray(d)||d.some((function(e){return"string"!=typeof e}))))return n(a,t(l)),!1;break;case"shared":if((!Array.isArray(d)||d.some((function(e){return"string"!=typeof e})))&&"owners"!==d&&"boolean"!=typeof d)return n(a,t(l)),!1;break;default:if(!hp.id[l])return console.error("customIntegration does not support key ".concat(l)),!1}}}return!0}function xp(e,t){if(void 0===t)return!1;switch(i(t)){case"string":return i(e)===t;case"object":if("object"!==i(e))return!1;for(var n in e)if(!xp(e[n],t[n]))return!1;return!0;default:return!1}}function Ap(e){if("object"!==i(e))throw new Error('RemoteMediaPlayerSettings: must be "object" type');if(e.state&&!Object.values(xo).includes(e.state))throw new Error("Invalid value for RemoteMediaPlayerSettings.state, valid values are: "+JSON.stringify(xo));if(e.volume){if("number"!=typeof e.volume)throw new Error('RemoteMediaPlayerSettings.volume: must be "number" type');if(e.volume<0||e.volume>2)throw new Error("RemoteMediaPlayerSettings.volume: must be between 0.0 - 2.0")}}function Dp(e,t,n){return!("number"!=typeof e||e<t||e>n)}function Lp(e,t){return e&&!t&&delete e.data,e}export{Kr as DAILY_ACCESS_LEVEL_FULL,Qr as DAILY_ACCESS_LEVEL_LOBBY,Xr as DAILY_ACCESS_LEVEL_NONE,Hr as DAILY_ACCESS_UNKNOWN,pi as DAILY_CAMERA_ERROR_CAM_AND_MIC_IN_USE,di as DAILY_CAMERA_ERROR_CAM_IN_USE,mi as DAILY_CAMERA_ERROR_CONSTRAINTS,hi as DAILY_CAMERA_ERROR_MIC_IN_USE,gi as DAILY_CAMERA_ERROR_NOT_FOUND,fi as DAILY_CAMERA_ERROR_PERMISSIONS,vi as DAILY_CAMERA_ERROR_UNDEF_MEDIADEVICES,yi as DAILY_CAMERA_ERROR_UNKNOWN,Ni as DAILY_EVENT_ACCESS_STATE_UPDATED,oo as DAILY_EVENT_ACTIVE_SPEAKER_CHANGE,ao as DAILY_EVENT_ACTIVE_SPEAKER_MODE_CHANGE,Zi as DAILY_EVENT_APP_MSG,Ci as DAILY_EVENT_CAMERA_ERROR,uo as DAILY_EVENT_CPU_LOAD_CHANGE,wo as DAILY_EVENT_ERROR,ho as DAILY_EVENT_EXIT_FULLSCREEN,lo as DAILY_EVENT_FULLSCREEN,bi as DAILY_EVENT_IFRAME_LAUNCH_CONFIG,_i as DAILY_EVENT_IFRAME_READY_FOR_LAUNCH_CONFIG,_o as DAILY_EVENT_INPUT_SETTINGS_UPDATED,Oi as DAILY_EVENT_JOINED_MEETING,Ti as DAILY_EVENT_JOINING_MEETING,mo as DAILY_EVENT_LANG_UPDATED,ji as DAILY_EVENT_LEFT_MEETING,go as DAILY_EVENT_LIVE_STREAMING_ERROR,po as DAILY_EVENT_LIVE_STREAMING_STARTED,vo as DAILY_EVENT_LIVE_STREAMING_STOPPED,fo as DAILY_EVENT_LIVE_STREAMING_UPDATED,Ei as DAILY_EVENT_LOADED,Si as DAILY_EVENT_LOADING,ki as DAILY_EVENT_LOAD_ATTEMPT_FAILED,ro as DAILY_EVENT_LOCAL_SCREEN_SHARE_STARTED,io as DAILY_EVENT_LOCAL_SCREEN_SHARE_STOPPED,Bi as DAILY_EVENT_MEETING_SESSION_DATA_ERROR,Ri as DAILY_EVENT_MEETING_SESSION_STATE_UPDATED,Ii as DAILY_EVENT_MEETING_SESSION_SUMMARY_UPDATED,co as DAILY_EVENT_NETWORK_CONNECTION,so as DAILY_EVENT_NETWORK_QUALITY_CHANGE,bo as DAILY_EVENT_NONFATAL_ERROR,Li as DAILY_EVENT_PARTICIPANT_COUNTS_UPDATED,xi as DAILY_EVENT_PARTICIPANT_JOINED,Di as DAILY_EVENT_PARTICIPANT_LEFT,Ai as DAILY_EVENT_PARTICIPANT_UPDATED,yo as DAILY_EVENT_RECEIVE_SETTINGS_UPDATED,Xi as DAILY_EVENT_RECORDING_DATA,Ki as DAILY_EVENT_RECORDING_ERROR,Ji as DAILY_EVENT_RECORDING_STARTED,Hi as DAILY_EVENT_RECORDING_STATS,Wi as DAILY_EVENT_RECORDING_STOPPED,Qi as DAILY_EVENT_RECORDING_UPLOAD_COMPLETED,eo as DAILY_EVENT_REMOTE_MEDIA_PLAYER_STARTED,no as DAILY_EVENT_REMOTE_MEDIA_PLAYER_STOPPED,to as DAILY_EVENT_REMOTE_MEDIA_PLAYER_UPDATED,Mi as DAILY_EVENT_STARTED_CAMERA,wi as DAILY_EVENT_THEME_UPDATED,$i as DAILY_EVENT_TRACK_STARTED,Gi as DAILY_EVENT_TRACK_STOPPED,zi as DAILY_EVENT_TRANSCRIPTION_ERROR,qi as DAILY_EVENT_TRANSCRIPTION_STARTED,Yi as DAILY_EVENT_TRANSCRIPTION_STOPPED,Fi as DAILY_EVENT_WAITING_PARTICIPANT_ADDED,Vi as DAILY_EVENT_WAITING_PARTICIPANT_REMOVED,Ui as DAILY_EVENT_WAITING_PARTICIPANT_UPDATED,li as DAILY_FATAL_ERROR_CONNECTION,ti as DAILY_FATAL_ERROR_EJECTED,ci as DAILY_FATAL_ERROR_EOL,ii as DAILY_FATAL_ERROR_EXP_ROOM,oi as DAILY_FATAL_ERROR_EXP_TOKEN,si as DAILY_FATAL_ERROR_MEETING_FULL,ni as DAILY_FATAL_ERROR_NBF_ROOM,ri as DAILY_FATAL_ERROR_NBF_TOKEN,ui as DAILY_FATAL_ERROR_NOT_ALLOWED,ai as DAILY_FATAL_ERROR_NO_ROOM,ei as DAILY_RECEIVE_SETTINGS_ALL_PARTICIPANTS_KEY,Zr as DAILY_RECEIVE_SETTINGS_BASE_KEY,$r as DAILY_STATE_ERROR,Ur as DAILY_STATE_JOINED,Fr as DAILY_STATE_JOINING,Vr as DAILY_STATE_LEFT,Ir as DAILY_STATE_NEW,Gr as DAILY_TRACK_STATE_BLOCKED,Jr as DAILY_TRACK_STATE_INTERRUPTED,zr as DAILY_TRACK_STATE_LOADING,qr as DAILY_TRACK_STATE_OFF,Wr as DAILY_TRACK_STATE_PLAYABLE,Yr as DAILY_TRACK_STATE_SENDABLE,vp as default};
